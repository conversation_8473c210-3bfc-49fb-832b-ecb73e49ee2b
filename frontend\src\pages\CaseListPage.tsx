import React, { useState } from 'react'
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  AppBar,
  Toolbar,
  Button,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material'
import { Search, FilterList } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const CaseListPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [levelFilter, setLevelFilter] = useState('all')

  // 模拟案例数据
  const cases = [
    {
      id: 1,
      title: '一次函数动画',
      description: '通过动画演示一次函数 y = ax + b 的图像变化，理解参数对函数的影响。',
      level: 'basic',
      category: '代数',
      icon: '📈',
      duration: '3分钟',
      viewCount: 1250,
    },
    {
      id: 2,
      title: '几何变换',
      description: '展示平移、旋转、缩放等基本几何变换，帮助理解图形变化规律。',
      level: 'basic',
      category: '几何',
      icon: '🔄',
      duration: '4分钟',
      viewCount: 980,
    },
    {
      id: 3,
      title: '三角函数',
      description: '动态展示正弦、余弦函数的图像生成过程和周期性特征。',
      level: 'intermediate',
      category: '三角函数',
      icon: '〰️',
      duration: '5分钟',
      viewCount: 756,
    },
    {
      id: 4,
      title: '二次函数图像',
      description: '演示二次函数的开口方向、顶点位置等特征的变化过程。',
      level: 'intermediate',
      category: '代数',
      icon: '📊',
      duration: '4分钟',
      viewCount: 642,
    },
    {
      id: 5,
      title: '圆的性质',
      description: '通过动画展示圆的周长、面积公式推导和相关性质。',
      level: 'basic',
      category: '几何',
      icon: '⭕',
      duration: '6分钟',
      viewCount: 523,
    },
    {
      id: 6,
      title: '导数概念',
      description: '可视化导数的几何意义，展示切线斜率的变化过程。',
      level: 'advanced',
      category: '微积分',
      icon: '📐',
      duration: '7分钟',
      viewCount: 389,
    },
  ]

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'basic':
        return '#4caf50'
      case 'intermediate':
        return '#ff9800'
      case 'advanced':
        return '#f44336'
      default:
        return '#9e9e9e'
    }
  }

  const getLevelText = (level: string) => {
    switch (level) {
      case 'basic':
        return '基础'
      case 'intermediate':
        return '中级'
      case 'advanced':
        return '高级'
      default:
        return '未知'
    }
  }

  // 过滤案例
  const filteredCases = cases.filter((case_) => {
    const matchesSearch = case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || case_.category === categoryFilter
    const matchesLevel = levelFilter === 'all' || case_.level === levelFilter
    
    return matchesSearch && matchesCategory && matchesLevel
  })

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      {/* 导航栏 */}
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            🧮 数学家-WEB
          </Typography>
          <Button color="inherit" onClick={() => navigate('/')}>
            首页
          </Button>
          <Button color="inherit" onClick={() => navigate('/login')}>
            登录
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* 页面标题 */}
        <Typography variant="h3" component="h1" sx={{ mb: 4, fontWeight: 600, textAlign: 'center' }}>
          数学动画案例库
        </Typography>

        {/* 搜索和筛选 */}
        <Box sx={{ mb: 4 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="搜索案例..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>分类</InputLabel>
                <Select
                  value={categoryFilter}
                  label="分类"
                  onChange={(e) => setCategoryFilter(e.target.value)}
                >
                  <MenuItem value="all">全部分类</MenuItem>
                  <MenuItem value="代数">代数</MenuItem>
                  <MenuItem value="几何">几何</MenuItem>
                  <MenuItem value="三角函数">三角函数</MenuItem>
                  <MenuItem value="微积分">微积分</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>难度</InputLabel>
                <Select
                  value={levelFilter}
                  label="难度"
                  onChange={(e) => setLevelFilter(e.target.value)}
                >
                  <MenuItem value="all">全部难度</MenuItem>
                  <MenuItem value="basic">基础</MenuItem>
                  <MenuItem value="intermediate">中级</MenuItem>
                  <MenuItem value="advanced">高级</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        {/* 结果统计 */}
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="body1" color="text.secondary">
            找到 {filteredCases.length} 个案例
          </Typography>
          {(searchTerm || categoryFilter !== 'all' || levelFilter !== 'all') && (
            <Button
              size="small"
              onClick={() => {
                setSearchTerm('')
                setCategoryFilter('all')
                setLevelFilter('all')
              }}
            >
              清除筛选
            </Button>
          )}
        </Box>

        {/* 案例网格 */}
        <Grid container spacing={3}>
          {filteredCases.map((case_) => (
            <Grid item xs={12} sm={6} md={4} key={case_.id}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 28px rgba(0,0,0,0.15)',
                  },
                }}
                onClick={() => navigate(`/cases/${case_.id}`)}
              >
                <Box
                  sx={{
                    height: 180,
                    background: 'linear-gradient(45deg, #667eea, #764ba2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '4rem',
                    color: 'white',
                  }}
                >
                  {case_.icon}
                </Box>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Typography variant="h6" component="h3" sx={{ fontWeight: 600, flex: 1 }}>
                      {case_.title}
                    </Typography>
                    <Chip
                      label={getLevelText(case_.level)}
                      size="small"
                      sx={{
                        bgcolor: getLevelColor(case_.level),
                        color: 'white',
                        fontWeight: 500,
                        ml: 1,
                      }}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.5 }}>
                    {case_.description}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      {case_.category} • {case_.duration}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {case_.viewCount} 次观看
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* 空状态 */}
        {filteredCases.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              没有找到匹配的案例
            </Typography>
            <Typography variant="body2" color="text.secondary">
              尝试调整搜索条件或筛选器
            </Typography>
          </Box>
        )}
      </Container>
    </Box>
  )
}

export default CaseListPage
