# 数学家-WEB 快速启动指南

## 环境要求

在开始之前，请确保您的系统已安装以下软件：

- **Java**: 17 或更高版本
- **Node.js**: 16 或更高版本
- **Python**: 3.8 或更高版本
- **PostgreSQL**: 13 或更高版本
- **Git**: 最新版本
- **Docker**: 可选，用于容器化部署

## 快速启动

### 方式一：Docker 部署（推荐）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 数学家-web
   ```

2. **启动所有服务**
   ```bash
   cd docker
   docker-compose up -d
   ```

3. **访问应用**
   - 前端应用: http://localhost:3000
   - 后端API: http://localhost:8080
   - API文档: http://localhost:8080/swagger-ui.html

### 方式二：本地开发部署

#### 1. 数据库设置

```bash
# 启动PostgreSQL服务
# Windows: 通过服务管理器启动
# Linux/Mac: sudo systemctl start postgresql

# 创建数据库
psql -U postgres
CREATE DATABASE mathweb_dev;
CREATE USER dev_user WITH PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE mathweb_dev TO dev_user;
\q
```

#### 2. Python环境设置

```bash
# 安装Python依赖
pip install -r requirements.txt

# 验证Manim安装
manim --version
```

#### 3. 后端启动

```bash
cd backend

# 使用Maven启动（Windows）
.\mvnw.cmd spring-boot:run

# 使用Maven启动（Linux/Mac）
./mvnw spring-boot:run
```

#### 4. 前端启动

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 验证安装

### 1. 测试Manim动画生成

```bash
cd manim-scripts/basic

# 生成一次函数动画
manim -ql linear_function.py LinearFunction

# 生成几何变换动画
manim -ql geometric_transformations.py GeometricTransformations
```

### 2. 测试后端API

```bash
# 健康检查
curl http://localhost:8080/api/actuator/health

# 获取案例列表（需要认证）
curl http://localhost:8080/api/cases
```

### 3. 测试前端应用

打开浏览器访问 http://localhost:3000，应该能看到React应用的欢迎页面。

## 开发工作流

### 添加新的数学案例

1. **创建Manim脚本**
   ```bash
   # 复制模板
   cp manim-scripts/templates/math_case_template.py manim-scripts/basic/new_case.py
   
   # 编辑脚本内容
   # 修改类名和动画逻辑
   ```

2. **测试动画生成**
   ```bash
   cd manim-scripts/basic
   manim -ql new_case.py NewCaseClass
   ```

3. **注册案例到数据库**
   - 通过管理员界面添加案例元数据
   - 或直接在数据库中插入记录

### 前端开发

```bash
cd frontend

# 启动开发服务器（热重载）
npm run dev

# 运行测试
npm run test

# 构建生产版本
npm run build
```

### 后端开发

```bash
cd backend

# 启动开发模式（热重载）
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev

# 运行测试
./mvnw test

# 构建JAR包
./mvnw clean package
```

## 常见问题

### Q: Manim安装失败
**A**: 确保已安装FFmpeg和LaTeX。Windows用户可以使用Chocolatey安装：
```bash
choco install ffmpeg miktex
```

### Q: 数据库连接失败
**A**: 检查PostgreSQL服务是否启动，数据库配置是否正确：
```bash
# 检查服务状态
pg_isready -h localhost -p 5432
```

### Q: 前端代理错误
**A**: 确保后端服务已启动，检查vite.config.ts中的代理配置。

### Q: Docker容器启动失败
**A**: 检查端口是否被占用，确保Docker服务正在运行：
```bash
docker ps
docker-compose logs
```

## 项目结构说明

```
数学家-web/
├── README.md                 # 项目说明
├── QUICK_START.md           # 快速启动指南
├── DEVELOPMENT_LOG.md       # 开发日志
├── PROJECT_SUMMARY.md       # 项目总结
├── requirements.txt         # Python依赖
├── frontend/               # React前端
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   └── package.json       # 前端依赖
├── backend/               # Spring Boot后端
│   ├── src/               # Java源代码
│   └── pom.xml           # Maven配置
├── manim-scripts/         # Manim动画脚本
│   ├── basic/            # 基础案例
│   ├── advanced/         # 高级案例
│   └── templates/        # 脚本模板
├── docs/                 # 项目文档
└── docker/               # Docker配置
```

## 开发建议

1. **代码规范**: 遵循项目的代码规范，使用ESLint和Prettier
2. **提交规范**: 使用Conventional Commits格式
3. **测试驱动**: 编写单元测试和集成测试
4. **文档更新**: 及时更新相关文档

## 获取帮助

- **项目文档**: 查看docs/目录下的详细文档
- **开发日志**: 查看DEVELOPMENT_LOG.md了解开发进度
- **问题反馈**: 通过GitHub Issues提交问题
- **技术支持**: 联系项目维护团队

## 下一步

1. 阅读[项目架构文档](math-web.md)了解详细设计
2. 查看[开发日志](DEVELOPMENT_LOG.md)了解开发进度
3. 参考[项目总结](PROJECT_SUMMARY.md)了解整体情况
4. 开始开发您的第一个数学动画案例！

---

**祝您开发愉快！** 🚀
