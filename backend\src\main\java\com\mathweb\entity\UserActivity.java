package com.mathweb.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户活动实体类
 * 记录用户对数学案例的访问和学习情况
 * 
 * <AUTHOR> Animator Team
 */
@Entity
@Table(name = "user_activities", indexes = {
    @Index(name = "idx_activity_user_case", columnList = "user_id, case_id", unique = true),
    @Index(name = "idx_activity_last_accessed", columnList = "last_accessed")
})
public class UserActivity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", referencedColumnName = "id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "case_id", referencedColumnName = "id", nullable = false)
    private MathCase mathCase;

    @Column(name = "access_count", nullable = false)
    private Integer accessCount = 0;

    @Column(name = "total_watch_time_seconds", nullable = false)
    private Integer totalWatchTimeSeconds = 0;

    @Column(name = "is_favorite", nullable = false)
    private Boolean isFavorite = false;

    @Column(name = "is_completed", nullable = false)
    private Boolean isCompleted = false;

    @Column(name = "completion_percentage")
    private Integer completionPercentage = 0;

    @Column(name = "first_accessed", nullable = false, updatable = false)
    private LocalDateTime firstAccessed;

    @Column(name = "last_accessed", nullable = false)
    private LocalDateTime lastAccessed;

    @Column(name = "last_position_seconds")
    private Integer lastPositionSeconds = 0;

    @Column(name = "notes", length = 1000)
    private String notes;

    // 构造函数
    public UserActivity() {
    }

    public UserActivity(User user, MathCase mathCase) {
        this.user = user;
        this.mathCase = mathCase;
        this.accessCount = 1;
        this.firstAccessed = LocalDateTime.now();
        this.lastAccessed = LocalDateTime.now();
    }

    // JPA生命周期回调
    @PrePersist
    protected void onCreate() {
        if (firstAccessed == null) {
            firstAccessed = LocalDateTime.now();
        }
        if (lastAccessed == null) {
            lastAccessed = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        lastAccessed = LocalDateTime.now();
    }

    // 业务方法
    public void recordAccess() {
        this.accessCount++;
        this.lastAccessed = LocalDateTime.now();
    }

    public void updateWatchTime(int additionalSeconds) {
        this.totalWatchTimeSeconds += additionalSeconds;
        this.lastAccessed = LocalDateTime.now();
    }

    public void updateProgress(int positionSeconds, int totalDurationSeconds) {
        this.lastPositionSeconds = positionSeconds;
        if (totalDurationSeconds > 0) {
            this.completionPercentage = Math.min(100, (positionSeconds * 100) / totalDurationSeconds);
            this.isCompleted = this.completionPercentage >= 90; // 观看90%以上认为完成
        }
        this.lastAccessed = LocalDateTime.now();
    }

    public void toggleFavorite() {
        this.isFavorite = !this.isFavorite;
        this.lastAccessed = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public MathCase getMathCase() {
        return mathCase;
    }

    public void setMathCase(MathCase mathCase) {
        this.mathCase = mathCase;
    }

    public Integer getAccessCount() {
        return accessCount;
    }

    public void setAccessCount(Integer accessCount) {
        this.accessCount = accessCount;
    }

    public Integer getTotalWatchTimeSeconds() {
        return totalWatchTimeSeconds;
    }

    public void setTotalWatchTimeSeconds(Integer totalWatchTimeSeconds) {
        this.totalWatchTimeSeconds = totalWatchTimeSeconds;
    }

    public Boolean getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(Boolean isFavorite) {
        this.isFavorite = isFavorite;
    }

    public Boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }

    public Integer getCompletionPercentage() {
        return completionPercentage;
    }

    public void setCompletionPercentage(Integer completionPercentage) {
        this.completionPercentage = completionPercentage;
    }

    public LocalDateTime getFirstAccessed() {
        return firstAccessed;
    }

    public void setFirstAccessed(LocalDateTime firstAccessed) {
        this.firstAccessed = firstAccessed;
    }

    public LocalDateTime getLastAccessed() {
        return lastAccessed;
    }

    public void setLastAccessed(LocalDateTime lastAccessed) {
        this.lastAccessed = lastAccessed;
    }

    public Integer getLastPositionSeconds() {
        return lastPositionSeconds;
    }

    public void setLastPositionSeconds(Integer lastPositionSeconds) {
        this.lastPositionSeconds = lastPositionSeconds;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // equals和hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserActivity that = (UserActivity) o;
        return Objects.equals(id, that.id) ||
               (Objects.equals(user, that.user) && Objects.equals(mathCase, that.mathCase));
    }

    @Override
    public int hashCode() {
        return Objects.hash(user, mathCase);
    }

    @Override
    public String toString() {
        return "UserActivity{" +
                "id=" + id +
                ", userId=" + (user != null ? user.getId() : null) +
                ", caseId=" + (mathCase != null ? mathCase.getId() : null) +
                ", accessCount=" + accessCount +
                ", totalWatchTimeSeconds=" + totalWatchTimeSeconds +
                ", isFavorite=" + isFavorite +
                ", isCompleted=" + isCompleted +
                ", completionPercentage=" + completionPercentage +
                ", lastAccessed=" + lastAccessed +
                '}';
    }
}
