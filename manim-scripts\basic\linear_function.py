"""
一次函数动画演示
展示一次函数 y = ax + b 的图像变化
适合中学生理解一次函数的概念
"""

from manim import *

class LinearFunction(Scene):
    def construct(self):
        # 设置标题
        title = Text("一次函数 y = ax + b", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)

        # 创建坐标系
        axes = Axes(
            x_range=[-5, 5, 1],
            y_range=[-5, 5, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE},
            x_axis_config={
                "numbers_to_include": np.arange(-4, 5, 2),
                "numbers_with_elongated_ticks": np.arange(-4, 5, 2),
            },
            y_axis_config={
                "numbers_to_include": np.arange(-4, 5, 2),
                "numbers_with_elongated_ticks": np.arange(-4, 5, 2),
            },
            tips=False,
        )
        axes_labels = axes.get_axis_labels(x_label="x", y_label="y")
        
        self.play(Create(axes), Write(axes_labels))
        self.wait(1)

        # 创建函数 y = x
        def linear_func_1(x):
            return x
        
        graph_1 = axes.plot(linear_func_1, color=RED, x_range=[-4, 4])
        equation_1 = MathTex("y = x", color=RED).to_corner(UL)
        
        self.play(Create(graph_1), Write(equation_1))
        self.wait(2)

        # 变换为 y = 2x
        def linear_func_2(x):
            return 2 * x
        
        graph_2 = axes.plot(linear_func_2, color=GREEN, x_range=[-2, 2])
        equation_2 = MathTex("y = 2x", color=GREEN).next_to(equation_1, DOWN)
        
        self.play(
            Transform(graph_1, graph_2),
            Write(equation_2)
        )
        self.wait(2)

        # 添加说明文字
        explanation_1 = Text("斜率 a 决定直线的倾斜程度", font_size=24, color=YELLOW)
        explanation_1.to_edge(DOWN)
        self.play(Write(explanation_1))
        self.wait(2)

        # 变换为 y = 2x + 1
        def linear_func_3(x):
            return 2 * x + 1
        
        graph_3 = axes.plot(linear_func_3, color=BLUE, x_range=[-2, 2])
        equation_3 = MathTex("y = 2x + 1", color=BLUE).next_to(equation_2, DOWN)
        
        self.play(
            Transform(graph_1, graph_3),
            Write(equation_3)
        )
        self.wait(2)

        # 更新说明文字
        explanation_2 = Text("常数项 b 决定直线在y轴上的截距", font_size=24, color=YELLOW)
        self.play(Transform(explanation_1, explanation_2))
        self.wait(2)

        # 显示截距点
        intercept_point = Dot(axes.coords_to_point(0, 1), color=ORANGE, radius=0.1)
        intercept_label = Text("y截距 = 1", font_size=20, color=ORANGE)
        intercept_label.next_to(intercept_point, RIGHT)
        
        self.play(Create(intercept_point), Write(intercept_label))
        self.wait(2)

        # 动态演示不同参数的效果
        self.play(FadeOut(explanation_1), FadeOut(intercept_point), FadeOut(intercept_label))
        
        # 创建参数显示
        a_tracker = ValueTracker(2)
        b_tracker = ValueTracker(1)
        
        def get_dynamic_graph():
            return axes.plot(
                lambda x: a_tracker.get_value() * x + b_tracker.get_value(),
                color=PURPLE,
                x_range=[-3, 3]
            )
        
        dynamic_graph = always_redraw(get_dynamic_graph)
        dynamic_equation = always_redraw(
            lambda: MathTex(
                f"y = {a_tracker.get_value():.1f}x + {b_tracker.get_value():.1f}",
                color=PURPLE
            ).to_corner(UR)
        )
        
        self.play(
            Transform(graph_1, dynamic_graph),
            Transform(equation_3, dynamic_equation)
        )
        
        # 动画演示参数变化
        self.play(a_tracker.animate.set_value(0.5), run_time=2)
        self.wait(1)
        self.play(a_tracker.animate.set_value(-1), run_time=2)
        self.wait(1)
        self.play(b_tracker.animate.set_value(-2), run_time=2)
        self.wait(1)
        self.play(b_tracker.animate.set_value(3), run_time=2)
        self.wait(1)

        # 总结
        summary = Text("一次函数的一般形式：y = ax + b", font_size=32, color=GOLD)
        summary.to_edge(DOWN)
        self.play(Write(summary))
        self.wait(3)

        # 结束动画
        self.play(FadeOut(Group(*self.mobjects)))
        
        end_text = Text("感谢观看！", font_size=48, color=BLUE)
        self.play(Write(end_text))
        self.wait(2)


class LinearFunctionInteractive(Scene):
    """
    交互式一次函数演示
    可以通过参数控制函数的形状
    """
    def construct(self):
        # 创建坐标系
        axes = Axes(
            x_range=[-5, 5, 1],
            y_range=[-5, 5, 1],
            x_length=10,
            y_length=8,
        )
        
        # 参数控制器
        a_value = ValueTracker(1)
        b_value = ValueTracker(0)
        
        # 动态函数图像
        def get_line():
            return axes.plot(
                lambda x: a_value.get_value() * x + b_value.get_value(),
                color=BLUE,
                x_range=[-4, 4]
            )
        
        line = always_redraw(get_line)
        
        # 动态方程显示
        equation = always_redraw(
            lambda: MathTex(
                f"y = {a_value.get_value():.1f}x + {b_value.get_value():.1f}"
            ).to_corner(UL)
        )
        
        self.add(axes, line, equation)
        
        # 演示不同的参数组合
        parameter_sets = [
            (2, 1),    # y = 2x + 1
            (-1, 2),   # y = -x + 2
            (0.5, -1), # y = 0.5x - 1
            (3, 0),    # y = 3x
        ]
        
        for a, b in parameter_sets:
            self.play(
                a_value.animate.set_value(a),
                b_value.animate.set_value(b),
                run_time=2
            )
            self.wait(1)
        
        self.wait(2)
