# 数学动画WEB平台配置文件
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: math-animator-backend
  
  profiles:
    active: dev
  
  datasource:
    url: ****************************************
    username: mathweb_user
    password: mathweb_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false
  
  security:
    user:
      name: admin
      password: admin123
  
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

# JWT配置
jwt:
  secret: math-animator-secret-key-2024-very-long-and-secure
  expiration: 86400000 # 24小时 (毫秒)
  refresh-expiration: 604800000 # 7天 (毫秒)

# Manim配置
manim:
  python-path: python
  script-base-path: ../manim-scripts
  output-base-path: ./animations
  quality: medium_quality # low_quality, medium_quality, high_quality
  cache-enabled: true
  cache-duration: 7 # 天数

# 文件存储配置
file:
  upload-dir: ./uploads
  animation-dir: ./animations
  max-size: 52428800 # 50MB

# 日志配置
logging:
  level:
    com.mathweb: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/math-animator.log

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
  info:
    title: Math Animator API
    description: 数学动画WEB平台API文档
    version: 1.0.0
    contact:
      name: Math Animator Team
      email: <EMAIL>

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: Math Animator Backend
    description: 数学动画WEB平台后端服务
    version: 1.0.0
    encoding: UTF-8
    java:
      version: 17

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: ****************************************_dev
    username: dev_user
    password: dev_password
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

logging:
  level:
    root: INFO
    com.mathweb: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: ${DATABASE_URL:****************************************_prod}
    username: ${DATABASE_USERNAME:prod_user}
    password: ${DATABASE_PASSWORD:prod_password}
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: WARN
    com.mathweb: INFO
  file:
    name: /var/log/math-animator/application.log
