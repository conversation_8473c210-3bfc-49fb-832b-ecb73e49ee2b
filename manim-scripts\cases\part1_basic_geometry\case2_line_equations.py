"""
案例2：直线的方程
知识要点：直线的点斜式、截距式、一般式
难度等级：⭐⭐⭐
"""

from manim import *
import numpy as np

class LineEquations(Scene):
    def construct(self):
        # 标题
        title = Text("案例2：直线的方程", font_size=36, color=BLUE)
        subtitle = Text("点斜式、截距式、一般式的相互转换", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.3)
        
        self.play(Write(title_group))
        self.wait(1)
        self.play(title_group.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[-3, 3, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE, "stroke_width": 2},
            tips=True
        )
        
        x_label = axes.get_x_axis_label("x")
        y_label = axes.get_y_axis_label("y")
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 1. 斜率的几何意义
        slope_text = Text("1. 斜率的几何意义", font_size=24, color=YELLOW)
        slope_text.to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        self.play(Write(slope_text))
        
        # 绘制一条直线
        k = 0.5  # 斜率
        b = 1    # y截距
        
        line = axes.plot(lambda x: k * x + b, x_range=[-4, 4], color=BLUE, stroke_width=3)
        
        # 选择两个点来展示斜率
        x1, x2 = 0, 2
        y1, y2 = k * x1 + b, k * x2 + b
        
        point1 = Dot(axes.coords_to_point(x1, y1), color=RED, radius=0.08)
        point2 = Dot(axes.coords_to_point(x2, y2), color=RED, radius=0.08)
        
        label1 = Text(f"({x1},{y1})", font_size=16, color=RED).next_to(point1, DOWN+LEFT)
        label2 = Text(f"({x2},{y2})", font_size=16, color=RED).next_to(point2, UP+RIGHT)
        
        self.play(Create(line))
        self.play(Create(point1), Create(point2), Write(label1), Write(label2))
        
        # 构造直角三角形显示斜率
        vertical_line = DashedLine(
            axes.coords_to_point(x2, y1),
            axes.coords_to_point(x2, y2),
            color=GREEN, stroke_width=2
        )
        horizontal_line = DashedLine(
            axes.coords_to_point(x1, y1),
            axes.coords_to_point(x2, y1),
            color=GREEN, stroke_width=2
        )
        
        # 标记Δx和Δy
        dx_label = Text(f"Δx = {x2-x1}", font_size=16, color=GREEN).next_to(horizontal_line, DOWN)
        dy_label = Text(f"Δy = {y2-y1}", font_size=16, color=GREEN).next_to(vertical_line, RIGHT)
        
        self.play(Create(vertical_line), Create(horizontal_line))
        self.play(Write(dx_label), Write(dy_label))
        
        # 斜率公式
        slope_formula = MathTex(
            f"k = \\frac{{\\Delta y}}{{\\Delta x}} = \\frac{{{y2-y1}}}{{{x2-x1}}} = {k}",
            font_size=20, color=GREEN
        ).to_edge(RIGHT, buff=0.5).shift(UP*2)
        
        self.play(Write(slope_formula))
        self.wait(2)
        
        # 清除辅助线
        self.play(FadeOut(vertical_line), FadeOut(horizontal_line), 
                 FadeOut(dx_label), FadeOut(dy_label), FadeOut(slope_formula))
        
        # 2. 点斜式方程
        self.play(slope_text.animate.become(Text("2. 点斜式方程", font_size=24, color=YELLOW).to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)))
        
        point_slope_formula = MathTex(
            f"y - y_1 = k(x - x_1)",
            font_size=24, color=BLUE
        ).to_edge(RIGHT, buff=0.5).shift(UP*1.5)
        
        specific_formula = MathTex(
            f"y - {y1} = {k}(x - {x1})",
            font_size=20, color=BLUE
        ).next_to(point_slope_formula, DOWN)
        
        self.play(Write(point_slope_formula))
        self.play(Write(specific_formula))
        self.wait(2)
        
        # 3. 截距式方程
        self.play(slope_text.animate.become(Text("3. 截距式方程", font_size=24, color=YELLOW).to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)))
        
        # 标记y截距
        y_intercept = Dot(axes.coords_to_point(0, b), color=ORANGE, radius=0.08)
        y_intercept_label = Text(f"y截距: {b}", font_size=16, color=ORANGE).next_to(y_intercept, LEFT)
        
        self.play(Create(y_intercept), Write(y_intercept_label))
        
        # 截距式公式
        intercept_formula = MathTex(
            f"y = kx + b = {k}x + {b}",
            font_size=24, color=ORANGE
        ).to_edge(RIGHT, buff=0.5).shift(DOWN*0.5)
        
        self.play(Write(intercept_formula))
        self.wait(2)
        
        # 4. 一般式方程
        self.play(slope_text.animate.become(Text("4. 一般式方程", font_size=24, color=YELLOW).to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)))
        
        # 转换为一般式
        # y = 0.5x + 1 → 0.5x - y + 1 = 0 → x - 2y + 2 = 0
        general_formula = MathTex(
            "Ax + By + C = 0",
            font_size=24, color=PURPLE
        ).to_edge(RIGHT, buff=0.5).shift(DOWN*1.5)
        
        specific_general = MathTex(
            f"{k}x - y + {b} = 0",
            font_size=20, color=PURPLE
        ).next_to(general_formula, DOWN)
        
        self.play(Write(general_formula))
        self.play(Write(specific_general))
        self.wait(2)
        
        # 5. 动态演示：改变斜率和截距
        self.play(FadeOut(point1), FadeOut(point2), FadeOut(label1), FadeOut(label2),
                 FadeOut(y_intercept), FadeOut(y_intercept_label))
        
        dynamic_text = Text("动态演示：参数变化对直线的影响", font_size=20, color=PINK)
        dynamic_text.to_edge(UP, buff=1.2)
        self.play(Write(dynamic_text))
        
        # 创建可变参数
        k_tracker = ValueTracker(0.5)
        b_tracker = ValueTracker(1)
        
        # 动态直线
        dynamic_line = always_redraw(lambda: 
            axes.plot(lambda x: k_tracker.get_value() * x + b_tracker.get_value(), 
                     x_range=[-4, 4], color=BLUE, stroke_width=3)
        )
        
        # 动态公式
        dynamic_equation = always_redraw(lambda:
            MathTex(f"y = {k_tracker.get_value():.1f}x + {b_tracker.get_value():.1f}",
                   font_size=24, color=BLUE).to_edge(DOWN, buff=0.5)
        )
        
        self.remove(line)
        self.add(dynamic_line, dynamic_equation)
        
        # 改变斜率
        self.play(k_tracker.animate.set_value(2), run_time=2)
        self.wait(1)
        self.play(k_tracker.animate.set_value(-1), run_time=2)
        self.wait(1)
        
        # 改变截距
        self.play(b_tracker.animate.set_value(-2), run_time=2)
        self.wait(1)
        self.play(b_tracker.animate.set_value(2), run_time=2)
        self.wait(1)
        
        # 总结
        conclusion = Text("几何直线 ↔ 代数方程", font_size=28, color=GREEN)
        conclusion.to_edge(DOWN, buff=0.2)
        
        self.play(Write(conclusion))
        self.wait(2)
        
        # 淡出
        self.play(*[FadeOut(mob) for mob in self.mobjects])
        self.wait(1)

class LineEquationsDemo(Scene):
    """简化版演示"""
    def construct(self):
        title = Text("直线的方程", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 直线
        line = axes.plot(lambda x: 0.5 * x + 1, color=BLUE, stroke_width=3)
        self.play(Create(line))
        
        # 方程形式
        equations = VGroup(
            MathTex("y = kx + b", color=YELLOW),
            MathTex("y - y_1 = k(x - x_1)", color=GREEN),
            MathTex("Ax + By + C = 0", color=RED)
        ).arrange(DOWN, buff=0.5).to_edge(RIGHT)
        
        for eq in equations:
            self.play(Write(eq))
            self.wait(1)
        
        self.wait(2)
