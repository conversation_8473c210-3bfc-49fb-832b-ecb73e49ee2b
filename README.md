# 数学家-WEB：中学生数学动画学习平台

## 项目简介

数学家-WEB是一个专为中学生设计的数学动画学习平台，通过Manim引擎生成高质量的数学动画，帮助学生更好地理解几何和代数概念。项目已实现基础功能，包括动画生成、前端展示和全屏播放等核心特性。

## 🎉 最新更新 (v1.0.0-dev)

- ✅ **完整前端界面** - 响应式设计，支持桌面和移动端
- ✅ **动画展示功能** - 直接在网页中播放Manim生成的数学动画
- ✅ **全屏播放体验** - 沉浸式全屏播放，支持完整播放控制
- ✅ **本地服务器** - 一键启动本地HTTP服务器
- ✅ **5个示例动画** - 涵盖基础测试、函数、几何等内容

## 核心特性

- 🎯 **面向中学生**: 友好的用户界面和适合的内容难度
- 📚 **丰富案例库**: 涵盖几何、代数等多个数学领域
- 🎬 **高质量动画**: 基于Manim引擎的专业数学动画
- 🔧 **模块化设计**: 便于管理员添加新的动画案例
- 👥 **多角色管理**: 支持学生、教师、管理员等不同角色
- 🖥️ **全屏播放**: 沉浸式动画观看体验
- 📱 **响应式设计**: 完美适配各种设备屏幕

## 技术架构

### 前端
- **框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit
- **UI组件**: Material-UI
- **构建工具**: Vite

### 后端
- **框架**: Spring Boot 3.x
- **安全**: Spring Security + JWT
- **数据库**: PostgreSQL 13+
- **API文档**: OpenAPI 3.0

### 动画引擎
- **引擎**: Manim Community Edition
- **语言**: Python 3.8+
- **渲染**: 本地进程调用

## 项目结构

```
数学家-web/
├── README.md                 # 项目说明
├── DEVELOPMENT_LOG.md        # 开发日志
├── math-web.md              # 架构文档
├── frontend/                # 前端应用
│   ├── src/
│   │   ├── components/      # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
├── backend/                 # 后端应用
│   ├── src/main/java/      # Java源码
│   │   └── com/mathweb/
│   │       ├── controller/ # 控制器
│   │       ├── service/    # 业务逻辑
│   │       ├── repository/ # 数据访问
│   │       └── entity/     # 实体类
│   ├── src/main/resources/ # 配置文件
│   └── pom.xml            # Maven配置
├── manim-scripts/          # Manim动画脚本
│   ├── basic/             # 基础案例
│   ├── advanced/          # 高级案例
│   └── templates/         # 脚本模板
├── docs/                   # 项目文档
│   ├── api/               # API文档
│   ├── deployment/        # 部署文档
│   └── user-guide/        # 用户指南
└── docker/                # Docker配置
    ├── docker-compose.yml
    └── Dockerfile
```

## 🚀 快速启动

### 方式一：一键启动（推荐）

1. **确保Python环境**
   ```bash
   python --version  # 需要Python 3.8+
   pip install manim
   ```

2. **启动本地服务器**
   ```bash
   # Windows用户
   start_server.bat

   # Linux/Mac用户
   python start_server.py
   ```

3. **访问应用**
   - 主页面: <http://localhost:8000/frontend/index.html>
   - 全屏测试: <http://localhost:8000/frontend/test-fullscreen.html>

### 方式二：完整开发环境

#### 环境要求

- **Java**: 17+
- **Node.js**: 16+
- **Python**: 3.8+
- **PostgreSQL**: 13+
- **Git**: 最新版本

#### 安装步骤

1. **克隆项目**

   ```bash
   git clone <repository-url>
   cd 数学家-web
   ```

2. **安装Python依赖**

   ```bash
   pip install manim
   pip install -r requirements.txt
   ```

3. **生成示例动画**

   ```bash
   # 生成基础测试动画
   python -m manim -ql manim-scripts/basic/test_integration.py SimpleTest

   # 生成一次函数动画
   python -m manim -ql manim-scripts/basic/simple_linear_function.py SimpleLinearFunction

   # 生成几何变换动画
   python -m manim -ql manim-scripts/basic/simple_geometry.py SimpleGeometry

   # 生成集成测试动画
   python -m manim -ql manim-scripts/basic/test_integration.py IntegrationTest
   python -m manim -ql manim-scripts/basic/test_integration.py QuickDemo
   ```

4. **启动本地服务器**

   ```bash
   # 使用Python脚本
   python start_server.py

   # 或使用BAT文件（Windows）
   start_server.bat
   ```

5. **访问应用**
   - 主页面: <http://localhost:8000/frontend/index.html>
   - 全屏测试: <http://localhost:8000/frontend/test-fullscreen.html>
   - 项目文档: <http://localhost:8000/README.md>

## 📊 当前功能状态

### ✅ 已完成功能

- **🎬 动画生成系统**
  - Manim脚本编写和执行
  - 5个示例数学动画（基础测试、一次函数、几何变换、集成测试、快速演示）
  - 支持1080p60和480p15多种分辨率

- **🖥️ 前端展示界面**
  - 响应式设计，支持桌面、平板、手机
  - 动画卡片网格布局
  - 直接在网页中播放视频
  - 现代化UI设计，毛玻璃效果

- **🎯 全屏播放功能**
  - 沉浸式全屏播放体验
  - 完整播放控制（播放/暂停、重播、静音）
  - 键盘快捷键支持（ESC关闭）
  - 点击背景关闭功能

- **🚀 本地服务器**
  - Python HTTP服务器
  - 一键启动BAT脚本
  - 自动端口检测
  - 文件完整性检查

### 🚧 开发中功能

- **后端API系统** - Spring Boot框架搭建中
- **用户认证系统** - JWT认证机制设计中
- **数据库集成** - PostgreSQL数据模型设计中
- **管理员界面** - 案例管理功能开发中

### 📋 计划功能

- **更多数学案例** - 初中几何、代数、函数等
- **学习路径系统** - 结构化学习计划
- **用户进度跟踪** - 学习记录和成就系统
- **互动练习** - 配套练习题和测验
- **社区功能** - 用户讨论和分享

## 开发指南

### 添加新的数学案例

1. **创建Manim脚本**
   ```python
   # manim-scripts/basic/new_case.py
   from manim import *

   class NewMathCase(Scene):
       def construct(self):
           # 动画逻辑
           pass
   ```

2. **注册案例信息**
   - 通过管理员界面添加案例元数据
   - 或直接在数据库中插入记录

3. **测试动画生成**
   ```bash
   manim -ql manim-scripts/basic/new_case.py NewMathCase
   ```

### API开发

后端API遵循RESTful设计原则：

- `GET /api/cases` - 获取案例列表
- `GET /api/cases/{id}` - 获取案例详情
- `POST /api/cases/{id}/animate` - 生成动画
- `POST /api/auth/login` - 用户登录

### 前端开发

使用React Hooks和TypeScript开发：

```typescript
// 案例列表组件示例
const CaseList: React.FC = () => {
  const [cases, setCases] = useState<MathCase[]>([]);

  useEffect(() => {
    fetchCases().then(setCases);
  }, []);

  return (
    <div>
      {cases.map(case => (
        <CaseCard key={case.id} case={case} />
      ))}
    </div>
  );
};
```

## 部署

### Docker部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境

详细的生产环境部署指南请参考 [docs/deployment/](docs/deployment/)

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [维护者信息]
- 问题反馈: [GitHub Issues](issues-url)
- 邮箱: [contact-email]

## 致谢

- [Manim Community](https://www.manim.community/) - 优秀的数学动画引擎
- [3Blue1Brown](https://www.3blue1brown.com/) - 数学动画的灵感来源
- [Spring Boot](https://spring.io/projects/spring-boot) - 强大的Java框架
- [React](https://react.dev/) - 现代化的前端框架
