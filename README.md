# 数学家-WEB：中学生数学动画学习平台

## 项目简介

数学家-WEB是一个专为中学生设计的数学动画学习平台，通过Manim引擎生成高质量的数学动画，帮助学生更好地理解几何和代数概念。

## 核心特性

- 🎯 **面向中学生**: 友好的用户界面和适合的内容难度
- 📚 **丰富案例库**: 涵盖几何、代数等多个数学领域
- 🎬 **高质量动画**: 基于Manim引擎的专业数学动画
- 🔧 **模块化设计**: 便于管理员添加新的动画案例
- 👥 **多角色管理**: 支持学生、教师、管理员等不同角色

## 技术架构

### 前端
- **框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit
- **UI组件**: Material-UI
- **构建工具**: Vite

### 后端
- **框架**: Spring Boot 3.x
- **安全**: Spring Security + JWT
- **数据库**: PostgreSQL 13+
- **API文档**: OpenAPI 3.0

### 动画引擎
- **引擎**: Manim Community Edition
- **语言**: Python 3.8+
- **渲染**: 本地进程调用

## 项目结构

```
数学家-web/
├── README.md                 # 项目说明
├── DEVELOPMENT_LOG.md        # 开发日志
├── math-web.md              # 架构文档
├── frontend/                # 前端应用
│   ├── src/
│   │   ├── components/      # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
├── backend/                 # 后端应用
│   ├── src/main/java/      # Java源码
│   │   └── com/mathweb/
│   │       ├── controller/ # 控制器
│   │       ├── service/    # 业务逻辑
│   │       ├── repository/ # 数据访问
│   │       └── entity/     # 实体类
│   ├── src/main/resources/ # 配置文件
│   └── pom.xml            # Maven配置
├── manim-scripts/          # Manim动画脚本
│   ├── basic/             # 基础案例
│   ├── advanced/          # 高级案例
│   └── templates/         # 脚本模板
├── docs/                   # 项目文档
│   ├── api/               # API文档
│   ├── deployment/        # 部署文档
│   └── user-guide/        # 用户指南
└── docker/                # Docker配置
    ├── docker-compose.yml
    └── Dockerfile
```

## 快速开始

### 环境要求

- **Java**: 17+
- **Node.js**: 16+
- **Python**: 3.8+
- **PostgreSQL**: 13+
- **Git**: 最新版本

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 数学家-web
   ```

2. **安装Python依赖**
   ```bash
   pip install manim
   pip install -r requirements.txt
   ```

3. **启动数据库**
   ```bash
   # 使用Docker
   docker-compose up -d postgres
   
   # 或手动启动PostgreSQL服务
   ```

4. **启动后端**
   ```bash
   cd backend
   ./mvnw spring-boot:run
   ```

5. **启动前端**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

6. **访问应用**
   - 前端: http://localhost:3000
   - 后端API: http://localhost:8080
   - API文档: http://localhost:8080/swagger-ui.html

## 开发指南

### 添加新的数学案例

1. **创建Manim脚本**
   ```python
   # manim-scripts/basic/new_case.py
   from manim import *
   
   class NewMathCase(Scene):
       def construct(self):
           # 动画逻辑
           pass
   ```

2. **注册案例信息**
   - 通过管理员界面添加案例元数据
   - 或直接在数据库中插入记录

3. **测试动画生成**
   ```bash
   manim -ql manim-scripts/basic/new_case.py NewMathCase
   ```

### API开发

后端API遵循RESTful设计原则：

- `GET /api/cases` - 获取案例列表
- `GET /api/cases/{id}` - 获取案例详情
- `POST /api/cases/{id}/animate` - 生成动画
- `POST /api/auth/login` - 用户登录

### 前端开发

使用React Hooks和TypeScript开发：

```typescript
// 案例列表组件示例
const CaseList: React.FC = () => {
  const [cases, setCases] = useState<MathCase[]>([]);
  
  useEffect(() => {
    fetchCases().then(setCases);
  }, []);
  
  return (
    <div>
      {cases.map(case => (
        <CaseCard key={case.id} case={case} />
      ))}
    </div>
  );
};
```

## 部署

### Docker部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境

详细的生产环境部署指南请参考 [docs/deployment/](docs/deployment/)

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [维护者信息]
- 问题反馈: [GitHub Issues](issues-url)
- 邮箱: [contact-email]

## 致谢

- [Manim Community](https://www.manim.community/) - 优秀的数学动画引擎
- [3Blue1Brown](https://www.3blue1brown.com/) - 数学动画的灵感来源
- [Spring Boot](https://spring.io/projects/spring-boot) - 强大的Java框架
- [React](https://react.dev/) - 现代化的前端框架
