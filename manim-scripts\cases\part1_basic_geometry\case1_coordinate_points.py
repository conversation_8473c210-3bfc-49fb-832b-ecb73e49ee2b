"""
案例1：坐标系中的点
知识要点：点的坐标表示、距离公式
难度等级：⭐⭐
"""

from manim import *
import numpy as np

class CoordinatePoints(Scene):
    def construct(self):
        # 标题
        title = Text("案例1：坐标系中的点", font_size=36, color=BLUE)
        subtitle = Text("点的坐标表示与距离公式", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.3)
        
        self.play(Write(title_group))
        self.wait(1)
        self.play(title_group.animate.to_edge(UP))
        
        # 1. 坐标系的建立过程
        axes = Axes(
            x_range=[-5, 5, 1],
            y_range=[-4, 4, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE, "stroke_width": 2},
            tips=True
        )
        
        # 坐标轴标签
        x_label = axes.get_x_axis_label("x")
        y_label = axes.get_y_axis_label("y")
        
        # 原点标记
        origin_dot = Dot(axes.coords_to_point(0, 0), color=RED, radius=0.08)
        origin_label = Text("O(0,0)", font_size=20, color=RED).next_to(origin_dot, DOWN+LEFT, buff=0.1)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.play(Create(origin_dot), Write(origin_label))
        self.wait(1)
        
        # 2. 点在坐标系中的定位
        # 点A
        point_A = np.array([3, 2])
        dot_A = Dot(axes.coords_to_point(*point_A), color=BLUE, radius=0.08)
        label_A = Text("A(3,2)", font_size=20, color=BLUE).next_to(dot_A, UP+RIGHT, buff=0.1)
        
        # 显示坐标的构造过程
        x_line_A = DashedLine(
            axes.coords_to_point(0, point_A[1]),
            axes.coords_to_point(point_A[0], point_A[1]),
            color=BLUE, stroke_width=2
        )
        y_line_A = DashedLine(
            axes.coords_to_point(point_A[0], 0),
            axes.coords_to_point(point_A[0], point_A[1]),
            color=BLUE, stroke_width=2
        )
        
        self.play(Create(x_line_A), Create(y_line_A))
        self.play(Create(dot_A), Write(label_A))
        self.wait(1)
        
        # 点B
        point_B = np.array([-2, 3])
        dot_B = Dot(axes.coords_to_point(*point_B), color=GREEN, radius=0.08)
        label_B = Text("B(-2,3)", font_size=20, color=GREEN).next_to(dot_B, UP+LEFT, buff=0.1)
        
        x_line_B = DashedLine(
            axes.coords_to_point(0, point_B[1]),
            axes.coords_to_point(point_B[0], point_B[1]),
            color=GREEN, stroke_width=2
        )
        y_line_B = DashedLine(
            axes.coords_to_point(point_B[0], 0),
            axes.coords_to_point(point_B[0], point_B[1]),
            color=GREEN, stroke_width=2
        )
        
        self.play(Create(x_line_B), Create(y_line_B))
        self.play(Create(dot_B), Write(label_B))
        self.wait(1)
        
        # 3. 两点间距离的几何意义和代数计算
        # 连接两点
        line_AB = Line(
            axes.coords_to_point(*point_A),
            axes.coords_to_point(*point_B),
            color=YELLOW, stroke_width=3
        )
        
        self.play(Create(line_AB))
        self.wait(0.5)
        
        # 构造直角三角形
        point_C = np.array([point_A[0], point_B[1]])
        dot_C = Dot(axes.coords_to_point(*point_C), color=ORANGE, radius=0.06)
        
        line_AC = Line(
            axes.coords_to_point(*point_A),
            axes.coords_to_point(*point_C),
            color=ORANGE, stroke_width=2
        )
        line_BC = Line(
            axes.coords_to_point(*point_B),
            axes.coords_to_point(*point_C),
            color=ORANGE, stroke_width=2
        )
        
        # 直角标记
        right_angle = RightAngle(line_BC, line_AC, length=0.3, color=ORANGE)
        
        self.play(Create(dot_C), Create(line_AC), Create(line_BC), Create(right_angle))
        self.wait(1)
        
        # 标记边长
        dx = abs(point_A[0] - point_B[0])
        dy = abs(point_A[1] - point_B[1])
        
        dx_label = Text(f"Δx = {dx}", font_size=18, color=ORANGE).next_to(line_AC, DOWN, buff=0.1)
        dy_label = Text(f"Δy = {dy}", font_size=18, color=ORANGE).next_to(line_BC, LEFT, buff=0.1)
        
        self.play(Write(dx_label), Write(dy_label))
        self.wait(1)
        
        # 距离公式推导
        formula_box = Rectangle(width=8, height=2, color=WHITE, stroke_width=2)
        formula_box.to_edge(DOWN, buff=0.5)
        
        distance = np.sqrt(dx**2 + dy**2)
        formula_text = MathTex(
            r"d = \sqrt{(x_2-x_1)^2 + (y_2-y_1)^2}",
            font_size=32, color=WHITE
        ).move_to(formula_box.get_center() + UP*0.3)
        
        calculation = MathTex(
            f"d = \\sqrt{{({point_B[0]}-({point_A[0]}))^2 + ({point_B[1]}-{point_A[1]})^2}} = \\sqrt{{{dx}^2 + {dy}^2}} = \\sqrt{{{dx**2 + dy**2}}} ≈ {distance:.2f}",
            font_size=24, color=YELLOW
        ).move_to(formula_box.get_center() + DOWN*0.3)
        
        self.play(Create(formula_box))
        self.play(Write(formula_text))
        self.wait(1)
        self.play(Write(calculation))
        self.wait(2)
        
        # 动态演示：移动点B，观察距离变化
        moving_text = Text("动态演示：移动点B观察距离变化", font_size=20, color=PINK)
        moving_text.to_edge(UP, buff=1.5)
        
        self.play(Write(moving_text))
        
        # 创建动态距离标签
        distance_tracker = ValueTracker(distance)
        dynamic_distance = always_redraw(lambda: 
            Text(f"距离: {distance_tracker.get_value():.2f}", 
                 font_size=20, color=YELLOW).next_to(line_AB, UP, buff=0.1)
        )
        
        self.add(dynamic_distance)
        
        # 移动点B到几个不同位置
        new_positions = [[-1, -2], [4, 1], [0, 4]]
        
        for new_pos in new_positions:
            new_distance = np.sqrt((new_pos[0] - point_A[0])**2 + (new_pos[1] - point_A[1])**2)
            
            self.play(
                dot_B.animate.move_to(axes.coords_to_point(*new_pos)),
                label_B.animate.next_to(axes.coords_to_point(*new_pos), UP+LEFT, buff=0.1),
                line_AB.animate.put_start_and_end_on(
                    axes.coords_to_point(*point_A),
                    axes.coords_to_point(*new_pos)
                ),
                distance_tracker.animate.set_value(new_distance),
                run_time=2
            )
            self.wait(1)
        
        # 总结
        conclusion = Text("几何距离 ↔ 代数公式", font_size=28, color=GREEN)
        conclusion.to_edge(DOWN, buff=0.2)
        
        self.play(Write(conclusion))
        self.wait(2)
        
        # 淡出
        self.play(*[FadeOut(mob) for mob in self.mobjects])
        self.wait(1)

class CoordinatePointsDemo(Scene):
    """简化版演示"""
    def construct(self):
        title = Text("坐标系中的点", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 简单的坐标系和两点距离演示
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 两个点
        A = Dot(axes.coords_to_point(2, 1), color=BLUE)
        B = Dot(axes.coords_to_point(-1, 2), color=RED)
        
        A_label = Text("A(2,1)", font_size=16).next_to(A, UR)
        B_label = Text("B(-1,2)", font_size=16).next_to(B, UL)
        
        self.play(Create(A), Create(B), Write(A_label), Write(B_label))
        
        # 距离线
        line = Line(A.get_center(), B.get_center(), color=YELLOW)
        self.play(Create(line))
        
        # 距离公式
        formula = MathTex(r"d = \sqrt{(x_2-x_1)^2 + (y_2-y_1)^2}", font_size=24)
        formula.to_edge(DOWN)
        self.play(Write(formula))
        
        self.wait(3)
