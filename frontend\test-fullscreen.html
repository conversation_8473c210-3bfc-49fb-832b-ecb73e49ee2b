<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏播放功能测试 - 数学家-WEB</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        /* 全屏播放器样式 */
        .fullscreen-player {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.95);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .fullscreen-video {
            max-width: 90vw;
            max-height: 80vh;
            border-radius: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }

        .fullscreen-title {
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        .fullscreen-controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .close-btn {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .close-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .back-btn {
            display: inline-block;
            background: #6c757d;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 全屏播放功能测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong> 点击下方按钮测试不同动画的全屏播放功能。支持键盘ESC键和点击背景关闭全屏。
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>基础测试动画</h3>
                <p>验证Manim环境基础功能</p>
                <button class="test-btn" onclick="openFullscreen('SimpleTest', '基础测试动画', '../media/videos/1080p60/SimpleTest.mp4')">
                    🔍 全屏播放
                </button>
            </div>

            <div class="test-card">
                <h3>一次函数动画</h3>
                <p>演示y=ax+b的图像变化</p>
                <button class="test-btn" onclick="openFullscreen('SimpleLinearFunction', '一次函数动画', '../media/videos/simple_linear_function/480p15/SimpleLinearFunction.mp4')">
                    🔍 全屏播放
                </button>
            </div>

            <div class="test-card">
                <h3>几何变换动画</h3>
                <p>展示基本几何变换</p>
                <button class="test-btn" onclick="openFullscreen('SimpleGeometry', '几何变换动画', '../media/videos/simple_geometry/480p15/SimpleGeometry.mp4')">
                    🔍 全屏播放
                </button>
            </div>

            <div class="test-card">
                <h3>系统集成测试</h3>
                <p>完整功能验证</p>
                <button class="test-btn" onclick="openFullscreen('IntegrationTest', '系统集成测试', '../media/videos/test_integration/480p15/IntegrationTest.mp4')">
                    🔍 全屏播放
                </button>
            </div>

            <div class="test-card">
                <h3>快速演示动画</h3>
                <p>综合效果展示</p>
                <button class="test-btn" onclick="openFullscreen('QuickDemo', '快速演示动画', '../media/videos/test_integration/480p15/QuickDemo.mp4')">
                    🔍 全屏播放
                </button>
            </div>
        </div>

        <div class="info">
            <strong>功能特性：</strong>
            <ul>
                <li>✅ 全屏沉浸式播放体验</li>
                <li>✅ 完整的播放控制功能</li>
                <li>✅ 键盘快捷键支持 (ESC关闭)</li>
                <li>✅ 点击背景关闭功能</li>
                <li>✅ 响应式设计适配</li>
                <li>✅ 优雅的动画过渡效果</li>
            </ul>
        </div>

        <a href="index.html" class="back-btn">← 返回主页</a>
    </div>

    <!-- 全屏播放器 -->
    <div id="fullscreen-player" class="fullscreen-player">
        <button class="close-btn" onclick="closeFullscreen()">×</button>
        <div class="fullscreen-title" id="fullscreen-title">动画标题</div>
        <video id="fullscreen-video" class="fullscreen-video" controls autoplay>
            <source id="fullscreen-source" src="" type="video/mp4">
            您的浏览器不支持视频播放。
        </video>
        <div class="fullscreen-controls">
            <button class="control-btn" onclick="restartVideo()">🔄 重新播放</button>
            <button class="control-btn" onclick="togglePlayPause()">⏯️ 播放/暂停</button>
            <button class="control-btn" onclick="toggleMute()">🔊 静音/取消静音</button>
            <button class="control-btn" onclick="closeFullscreen()">❌ 关闭</button>
        </div>
    </div>

    <script>
        // 全屏播放功能
        function openFullscreen(animationId, title, videoSrc) {
            const fullscreenPlayer = document.getElementById('fullscreen-player');
            const fullscreenTitle = document.getElementById('fullscreen-title');
            const fullscreenVideo = document.getElementById('fullscreen-video');
            const fullscreenSource = document.getElementById('fullscreen-source');
            
            // 设置标题和视频源
            fullscreenTitle.textContent = title;
            fullscreenSource.src = videoSrc;
            
            // 重新加载视频
            fullscreenVideo.load();
            
            // 显示全屏播放器
            fullscreenPlayer.style.display = 'flex';
            
            // 禁用页面滚动
            document.body.style.overflow = 'hidden';
            
            // 播放视频
            fullscreenVideo.play().catch(error => {
                console.error('全屏播放失败:', error);
                alert('视频播放失败，请检查文件路径是否正确。\n\n文件路径: ' + videoSrc);
            });
        }

        function closeFullscreen() {
            const fullscreenPlayer = document.getElementById('fullscreen-player');
            const fullscreenVideo = document.getElementById('fullscreen-video');
            
            // 暂停视频
            fullscreenVideo.pause();
            
            // 隐藏全屏播放器
            fullscreenPlayer.style.display = 'none';
            
            // 恢复页面滚动
            document.body.style.overflow = 'auto';
        }

        function restartVideo() {
            const fullscreenVideo = document.getElementById('fullscreen-video');
            fullscreenVideo.currentTime = 0;
            fullscreenVideo.play();
        }

        function togglePlayPause() {
            const fullscreenVideo = document.getElementById('fullscreen-video');
            if (fullscreenVideo.paused) {
                fullscreenVideo.play();
            } else {
                fullscreenVideo.pause();
            }
        }

        function toggleMute() {
            const fullscreenVideo = document.getElementById('fullscreen-video');
            fullscreenVideo.muted = !fullscreenVideo.muted;
        }

        // ESC键关闭全屏
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeFullscreen();
            }
        });

        // 点击背景关闭全屏
        document.getElementById('fullscreen-player').addEventListener('click', function(event) {
            if (event.target === this) {
                closeFullscreen();
            }
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.test-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
