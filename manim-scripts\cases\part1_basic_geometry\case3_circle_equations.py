"""
案例3：圆的方程
知识要点：圆的标准方程、一般方程
难度等级：⭐⭐⭐
"""

from manim import *
import numpy as np

class CircleEquations(Scene):
    def construct(self):
        # 标题
        title = Text("案例3：圆的方程", font_size=36, color=BLUE)
        subtitle = Text("圆的形成过程与方程推导", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.3)
        
        self.play(Write(title_group))
        self.wait(1)
        self.play(title_group.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(
            x_range=[-5, 5, 1],
            y_range=[-4, 4, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE, "stroke_width": 2},
            tips=True
        )
        
        x_label = axes.get_x_axis_label("x")
        y_label = axes.get_y_axis_label("y")
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 1. 圆的形成过程（定点定长）
        formation_text = Text("1. 圆的形成：到定点距离等于定长的点的轨迹", font_size=20, color=YELLOW)
        formation_text.to_edge(UP, buff=1.5)
        self.play(Write(formation_text))
        
        # 圆心
        center = np.array([1, 0.5])
        radius = 2
        
        center_dot = Dot(axes.coords_to_point(*center), color=RED, radius=0.08)
        center_label = Text(f"圆心O({center[0]},{center[1]})", font_size=16, color=RED)
        center_label.next_to(center_dot, DOWN+RIGHT, buff=0.1)
        
        self.play(Create(center_dot), Write(center_label))
        self.wait(1)
        
        # 动态绘制圆
        circle = Circle(radius=radius, color=BLUE, stroke_width=3)
        circle.move_to(axes.coords_to_point(*center))
        
        # 用参数方程绘制圆，展示形成过程
        def circle_point(t):
            x = center[0] + radius * np.cos(t)
            y = center[1] + radius * np.sin(t)
            return axes.coords_to_point(x, y)
        
        # 创建一个点沿着圆周运动
        moving_dot = Dot(circle_point(0), color=GREEN, radius=0.06)
        radius_line = always_redraw(lambda: 
            Line(center_dot.get_center(), moving_dot.get_center(), 
                 color=GREEN, stroke_width=2)
        )
        
        # 半径标签
        radius_label = always_redraw(lambda:
            Text(f"r = {radius}", font_size=16, color=GREEN)
            .next_to(radius_line.get_center(), UP, buff=0.1)
        )
        
        self.add(radius_line, radius_label)
        self.play(Create(moving_dot))
        
        # 让点绕圆心转动，形成圆
        self.play(
            MoveAlongPath(moving_dot, circle),
            Create(circle),
            run_time=4,
            rate_func=linear
        )
        self.wait(1)
        
        # 2. 标准方程推导
        self.play(formation_text.animate.become(
            Text("2. 标准方程推导", font_size=20, color=YELLOW)
            .to_edge(UP, buff=1.5)
        ))
        
        # 在圆上任取一点
        angle = PI/4
        point_on_circle = np.array([
            center[0] + radius * np.cos(angle),
            center[1] + radius * np.sin(angle)
        ])
        
        point_dot = Dot(axes.coords_to_point(*point_on_circle), color=ORANGE, radius=0.08)
        point_label = Text(f"P({point_on_circle[0]:.1f},{point_on_circle[1]:.1f})", 
                          font_size=16, color=ORANGE)
        point_label.next_to(point_dot, UP+RIGHT, buff=0.1)
        
        self.play(Create(point_dot), Write(point_label))
        
        # 距离公式
        distance_formula = MathTex(
            r"|OP| = \sqrt{(x-a)^2 + (y-b)^2} = r",
            font_size=24, color=WHITE
        ).to_edge(RIGHT, buff=0.5).shift(UP*1)
        
        self.play(Write(distance_formula))
        self.wait(1)
        
        # 标准方程
        standard_equation = MathTex(
            r"(x-a)^2 + (y-b)^2 = r^2",
            font_size=28, color=BLUE
        ).to_edge(RIGHT, buff=0.5)
        
        self.play(Write(standard_equation))
        self.wait(1)
        
        # 具体方程
        specific_equation = MathTex(
            f"(x-{center[0]})^2 + (y-{center[1]})^2 = {radius}^2",
            font_size=20, color=BLUE
        ).to_edge(RIGHT, buff=0.5).shift(DOWN*1)
        
        self.play(Write(specific_equation))
        self.wait(2)
        
        # 3. 参数变化对圆的影响
        self.play(formation_text.animate.become(
            Text("3. 参数变化对圆的影响", font_size=20, color=YELLOW)
            .to_edge(UP, buff=1.5)
        ))
        
        # 清除一些元素
        self.play(FadeOut(moving_dot), FadeOut(point_dot), FadeOut(point_label),
                 FadeOut(distance_formula))
        
        # 创建动态圆
        center_tracker_x = ValueTracker(center[0])
        center_tracker_y = ValueTracker(center[1])
        radius_tracker = ValueTracker(radius)
        
        dynamic_circle = always_redraw(lambda:
            Circle(radius=radius_tracker.get_value(), color=BLUE, stroke_width=3)
            .move_to(axes.coords_to_point(center_tracker_x.get_value(), 
                                        center_tracker_y.get_value()))
        )
        
        dynamic_center = always_redraw(lambda:
            Dot(axes.coords_to_point(center_tracker_x.get_value(), 
                                   center_tracker_y.get_value()), 
                color=RED, radius=0.08)
        )
        
        dynamic_equation = always_redraw(lambda:
            MathTex(
                f"(x-{center_tracker_x.get_value():.1f})^2 + (y-{center_tracker_y.get_value():.1f})^2 = {radius_tracker.get_value():.1f}^2",
                font_size=18, color=BLUE
            ).to_edge(DOWN, buff=0.5)
        )
        
        self.remove(circle, center_dot, specific_equation)
        self.add(dynamic_circle, dynamic_center, dynamic_equation)
        
        # 改变圆心位置
        self.play(
            center_tracker_x.animate.set_value(-1),
            center_tracker_y.animate.set_value(1),
            run_time=2
        )
        self.wait(1)
        
        # 改变半径
        self.play(radius_tracker.animate.set_value(1.5), run_time=2)
        self.wait(1)
        self.play(radius_tracker.animate.set_value(3), run_time=2)
        self.wait(1)
        
        # 4. 一般方程
        self.play(formation_text.animate.become(
            Text("4. 一般方程形式", font_size=20, color=YELLOW)
            .to_edge(UP, buff=1.5)
        ))
        
        # 展开标准方程得到一般方程
        general_form = MathTex(
            r"x^2 + y^2 + Dx + Ey + F = 0",
            font_size=24, color=PURPLE
        ).to_edge(RIGHT, buff=0.5).shift(UP*1)
        
        conversion_text = Text("展开标准方程可得一般方程", font_size=16, color=GRAY)
        conversion_text.next_to(general_form, DOWN)
        
        self.play(Write(general_form), Write(conversion_text))
        self.wait(2)
        
        # 总结
        conclusion = Text("几何圆 ↔ 代数方程", font_size=28, color=GREEN)
        conclusion.to_edge(DOWN, buff=0.2)
        
        self.play(Write(conclusion))
        self.wait(2)
        
        # 淡出
        self.play(*[FadeOut(mob) for mob in self.mobjects])
        self.wait(1)

class CircleEquationsDemo(Scene):
    """简化版演示"""
    def construct(self):
        title = Text("圆的方程", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 圆心和圆
        center = Dot(axes.coords_to_point(1, 0), color=RED)
        circle = Circle(radius=1.5, color=BLUE, stroke_width=3).move_to(center.get_center())
        
        self.play(Create(center), Create(circle))
        
        # 方程
        equation = MathTex(r"(x-a)^2 + (y-b)^2 = r^2", font_size=24, color=BLUE)
        equation.to_edge(DOWN)
        self.play(Write(equation))
        
        # 半径线
        radius_line = Line(center.get_center(), 
                          center.get_center() + RIGHT*1.5, 
                          color=GREEN, stroke_width=2)
        radius_label = Text("r", font_size=20, color=GREEN).next_to(radius_line, UP)
        
        self.play(Create(radius_line), Write(radius_label))
        self.wait(3)
