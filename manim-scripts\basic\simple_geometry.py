"""
简化版几何变换动画演示
不使用LaTeX，适合没有LaTeX环境的系统
"""

from manim import *

class SimpleGeometry(Scene):
    def construct(self):
        # 标题
        title = Text("几何变换演示", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)

        # 创建原始图形 - 一个三角形
        original_triangle = Polygon(
            [-2, -1, 0], [0, 1, 0], [2, -1, 0],
            color=RED,
            fill_opacity=0.5
        )
        
        # 添加标签
        original_label = Text("原图形", font_size=20, color=RED)
        original_label.next_to(original_triangle, DOWN)
        
        self.play(Create(original_triangle), Write(original_label))
        self.wait(2)

        # 1. 平移变换
        translation_title = Text("1. 平移变换", font_size=32, color=YELLOW)
        translation_title.to_edge(LEFT).shift(UP * 2)
        self.play(Write(translation_title))
        
        # 创建平移后的图形
        translated_triangle = original_triangle.copy()
        translated_triangle.set_color(GREEN)
        
        # 显示平移动画
        self.play(
            translated_triangle.animate.shift(RIGHT * 4 + UP * 1),
            run_time=2
        )
        
        translated_label = Text("平移后", font_size=20, color=GREEN)
        translated_label.next_to(translated_triangle, DOWN)
        self.play(Write(translated_label))
        
        # 显示平移向量
        translation_vector = Arrow(
            original_triangle.get_center(),
            translated_triangle.get_center(),
            color=YELLOW,
            buff=0.1
        )
        vector_label = Text("平移向量", font_size=16, color=YELLOW)
        vector_label.next_to(translation_vector, UP)
        
        self.play(Create(translation_vector), Write(vector_label))
        self.wait(2)

        # 清除平移相关元素
        self.play(
            FadeOut(translation_title),
            FadeOut(translated_triangle),
            FadeOut(translated_label),
            FadeOut(translation_vector),
            FadeOut(vector_label)
        )

        # 2. 旋转变换
        rotation_title = Text("2. 旋转变换", font_size=32, color=YELLOW)
        rotation_title.to_edge(LEFT).shift(UP * 2)
        self.play(Write(rotation_title))
        
        # 创建旋转中心点
        rotation_center = Dot(ORIGIN, color=ORANGE, radius=0.1)
        center_label = Text("旋转中心", font_size=16, color=ORANGE)
        center_label.next_to(rotation_center, DOWN)
        
        self.play(Create(rotation_center), Write(center_label))
        
        # 执行旋转动画
        rotated_triangle = original_triangle.copy()
        rotated_triangle.set_color(BLUE)
        
        self.play(
            Rotate(rotated_triangle, PI/3, about_point=ORIGIN),
            run_time=2
        )
        
        rotated_label = Text("旋转60度", font_size=20, color=BLUE)
        rotated_label.next_to(rotated_triangle, RIGHT)
        self.play(Write(rotated_label))
        self.wait(2)

        # 清除旋转相关元素
        self.play(
            FadeOut(rotation_title),
            FadeOut(rotated_triangle),
            FadeOut(rotated_label),
            FadeOut(rotation_center),
            FadeOut(center_label)
        )

        # 3. 缩放变换
        scaling_title = Text("3. 缩放变换", font_size=32, color=YELLOW)
        scaling_title.to_edge(LEFT).shift(UP * 2)
        self.play(Write(scaling_title))
        
        # 创建缩放后的图形
        scaled_triangle = original_triangle.copy()
        scaled_triangle.set_color(PURPLE)
        
        self.play(
            scaled_triangle.animate.scale(1.5),
            run_time=2
        )
        
        scaled_label = Text("放大1.5倍", font_size=20, color=PURPLE)
        scaled_label.next_to(scaled_triangle, UP)
        self.play(Write(scaled_label))
        self.wait(1)
        
        # 缩小
        small_triangle = original_triangle.copy()
        small_triangle.set_color(PINK)
        small_triangle.shift(RIGHT * 3)
        
        self.play(
            small_triangle.animate.scale(0.5),
            run_time=2
        )
        
        small_label = Text("缩小0.5倍", font_size=20, color=PINK)
        small_label.next_to(small_triangle, DOWN)
        self.play(Write(small_label))
        self.wait(2)

        # 清除所有元素
        self.play(FadeOut(Group(*self.mobjects)))

        # 4. 组合变换演示
        combo_title = Text("组合变换演示", font_size=36, color=GOLD)
        self.play(Write(combo_title))
        self.wait(1)
        
        # 重新创建原始图形
        shape = Square(side_length=2, color=RED, fill_opacity=0.6)
        self.play(Create(shape))
        self.wait(1)
        
        # 连续应用多种变换
        self.play(
            shape.animate.shift(RIGHT * 2),  # 平移
            run_time=1.5
        )
        self.play(
            Rotate(shape, PI/4),  # 旋转45度
            run_time=1.5
        )
        self.play(
            shape.animate.scale(0.7),  # 缩放
            run_time=1.5
        )
        self.play(
            shape.animate.set_color(BLUE),  # 改变颜色
            run_time=1
        )
        
        self.wait(2)
        
        # 结束语
        end_text = Text("几何变换让图形更加生动！", font_size=32, color=GREEN)
        end_text.to_edge(DOWN)
        self.play(Write(end_text))
        self.wait(3)


class TransformationShowcase(Scene):
    """
    变换展示场景
    同时显示多种变换的效果
    """
    def construct(self):
        # 创建网格背景
        grid = NumberPlane(
            x_range=[-6, 6, 1],
            y_range=[-4, 4, 1],
            background_line_style={
                "stroke_color": GREY,
                "stroke_width": 1,
                "stroke_opacity": 0.3
            }
        )
        self.add(grid)
        
        # 原始图形
        original = RegularPolygon(n=6, color=WHITE, fill_opacity=0.3)
        original.shift(LEFT * 4)
        
        # 各种变换
        translated = original.copy().shift(RIGHT * 2)
        translated.set_color(RED)
        
        rotated = original.copy().rotate(PI/3)
        rotated.set_color(GREEN)
        rotated.shift(UP * 2)
        
        scaled = original.copy().scale(1.5)
        scaled.set_color(BLUE)
        scaled.shift(RIGHT * 4)
        
        # 标签
        labels = [
            Text("原图", font_size=16).next_to(original, DOWN),
            Text("平移", font_size=16, color=RED).next_to(translated, DOWN),
            Text("旋转", font_size=16, color=GREEN).next_to(rotated, DOWN),
            Text("缩放", font_size=16, color=BLUE).next_to(scaled, DOWN),
        ]
        
        # 同时显示所有变换
        self.play(
            *[Create(shape) for shape in [original, translated, rotated, scaled]],
            *[Write(label) for label in labels],
            run_time=2
        )
        
        self.wait(3)
