{"name": "math-animator-frontend", "version": "1.0.0", "description": "数学动画WEB平台前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "axios": "^1.6.2", "react-player": "^2.13.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "framer-motion": "^10.16.5", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-hot-toast": "^2.4.1", "dayjs": "^1.11.10"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1"}, "keywords": ["react", "typescript", "vite", "math", "animation", "education"], "author": "Math Animator Team", "license": "MIT"}