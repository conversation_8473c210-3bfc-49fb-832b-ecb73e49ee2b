<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学家-WEB | 中学生数学动画学习平台</title>
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status h3 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        
        .status-list {
            text-align: left;
            color: #2e7d32;
        }
        
        .status-list li {
            margin: 5px 0;
            list-style: none;
            position: relative;
            padding-left: 25px;
        }
        
        .status-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .next-steps {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .next-steps h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .next-steps p {
            color: #856404;
            text-align: left;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🧮</div>
        <h1>数学家-WEB</h1>
        <p class="subtitle">中学生数学动画学习平台</p>
        
        <div class="features">
            <div class="feature">
                <h3>🎯 面向中学生</h3>
                <p>专为中学生设计的友好界面和适合的内容难度</p>
            </div>
            <div class="feature">
                <h3>🎬 高质量动画</h3>
                <p>基于Manim引擎的专业数学动画演示</p>
            </div>
            <div class="feature">
                <h3>📚 丰富案例</h3>
                <p>涵盖几何、代数、函数等多个数学领域</p>
            </div>
            <div class="feature">
                <h3>🔧 模块化设计</h3>
                <p>便于管理员添加新的数学动画案例</p>
            </div>
        </div>
        
        <div class="status">
            <h3>✅ 项目状态：开发中</h3>
            <ul class="status-list">
                <li>项目架构设计完成</li>
                <li>Manim动画引擎测试成功</li>
                <li>示例动画生成成功</li>
                <li>前端框架搭建完成</li>
                <li>后端框架搭建完成</li>
            </ul>
        </div>
        
        <div class="next-steps">
            <h3>🚀 下一步计划</h3>
            <p>
                1. 完善后端API接口<br>
                2. 开发前端React组件<br>
                3. 集成用户认证系统<br>
                4. 实现动画播放功能<br>
                5. 添加更多数学案例
            </p>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="#" class="btn" onclick="showAnimations()">查看示例动画</a>
            <a href="#" class="btn btn-secondary" onclick="showDocs()">查看文档</a>
        </div>
        
        <div style="margin-top: 20px; color: #666; font-size: 0.9rem;">
            <p>技术栈：React + Spring Boot + PostgreSQL + Manim</p>
            <p>版本：v1.0.0-dev</p>
        </div>
    </div>

    <script>
        function showAnimations() {
            alert('示例动画已生成在 media/videos/ 目录下：\n\n' +
                  '1. SimpleLinearFunction.mp4 - 一次函数动画\n' +
                  '2. SimpleGeometry.mp4 - 几何变换动画\n' +
                  '3. SimpleTest.mp4 - 基础测试动画\n\n' +
                  '请使用视频播放器查看这些动画文件。');
        }
        
        function showDocs() {
            alert('项目文档：\n\n' +
                  '• README.md - 项目说明\n' +
                  '• QUICK_START.md - 快速启动指南\n' +
                  '• DEVELOPMENT_LOG.md - 开发日志\n' +
                  '• PROJECT_SUMMARY.md - 项目总结\n' +
                  '• math-web.md - 详细架构文档');
        }
        
        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    feature.style.transition = 'all 0.5s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
