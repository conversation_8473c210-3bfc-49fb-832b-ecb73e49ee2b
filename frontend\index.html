<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学家-WEB | 中学生数学动画学习平台</title>
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            box-sizing: border-box;
        }

        .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .feature p {
            color: #666;
            font-size: 0.9rem;
        }

        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .status h3 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .status-list {
            text-align: left;
            color: #2e7d32;
        }

        .status-list li {
            margin: 5px 0;
            list-style: none;
            position: relative;
            padding-left: 25px;
        }

        .status-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }

        .next-steps {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .next-steps h3 {
            color: #856404;
            margin-bottom: 10px;
        }

        .next-steps p {
            color: #856404;
            text-align: left;
        }

        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* 动画展示区域样式 */
        .animations-section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }

        .animations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .animation-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .animation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .animation-preview {
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }

        .animation-preview video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

        .animation-preview .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .animation-preview .play-overlay:hover {
            background: rgba(0,0,0,0.9);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .animation-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .animation-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .animation-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #888;
        }

        .animation-duration {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
                width: calc(100% - 20px);
            }

            .animations-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .animation-preview {
                height: 150px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .logo {
                font-size: 2rem;
            }

            h1 {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🧮</div>
        <h1>数学家-WEB</h1>
        <p class="subtitle">中学生数学动画学习平台</p>

        <div class="features">
            <div class="feature">
                <h3>🎯 面向中学生</h3>
                <p>专为中学生设计的友好界面和适合的内容难度</p>
            </div>
            <div class="feature">
                <h3>🎬 高质量动画</h3>
                <p>基于Manim引擎的专业数学动画演示</p>
            </div>
            <div class="feature">
                <h3>📚 丰富案例</h3>
                <p>涵盖几何、代数、函数等多个数学领域</p>
            </div>
            <div class="feature">
                <h3>🔧 模块化设计</h3>
                <p>便于管理员添加新的数学动画案例</p>
            </div>
        </div>

        <div class="status">
            <h3>✅ 项目状态：开发中</h3>
            <ul class="status-list">
                <li>项目架构设计完成</li>
                <li>Manim动画引擎测试成功</li>
                <li>示例动画生成成功</li>
                <li>前端框架搭建完成</li>
                <li>后端框架搭建完成</li>
            </ul>
        </div>

        <!-- 动画展示区域 -->
        <div class="animations-section">
            <h3>🎬 生成的数学动画</h3>
            <p style="color: #666; margin-bottom: 20px;">点击下方动画卡片观看Manim生成的数学动画演示</p>

            <div class="animations-grid">
                <!-- 基础测试动画 -->
                <div class="animation-card" onclick="playAnimation('SimpleTest')">
                    <div class="animation-preview">
                        <video id="video-SimpleTest" preload="metadata" muted>
                            <source src="../media/videos/1080p60/SimpleTest.mp4" type="video/mp4">
                        </video>
                        <button class="play-overlay">▶</button>
                    </div>
                    <div class="animation-title">基础测试动画</div>
                    <div class="animation-description">验证Manim环境的基础功能测试，包含文本和几何图形的基本动画效果。</div>
                    <div class="animation-meta">
                        <span>测试 • 入门级</span>
                        <span class="animation-duration">~10秒</span>
                    </div>
                </div>

                <!-- 一次函数动画 -->
                <div class="animation-card" onclick="playAnimation('SimpleLinearFunction')">
                    <div class="animation-preview">
                        <video id="video-SimpleLinearFunction" preload="metadata" muted>
                            <source src="../media/videos/simple_linear_function/480p15/SimpleLinearFunction.mp4" type="video/mp4">
                        </video>
                        <button class="play-overlay">▶</button>
                    </div>
                    <div class="animation-title">一次函数动画</div>
                    <div class="animation-description">演示一次函数 y = ax + b 的图像变化，理解参数对函数图像的影响。</div>
                    <div class="animation-meta">
                        <span>代数 • 基础</span>
                        <span class="animation-duration">~20秒</span>
                    </div>
                </div>

                <!-- 几何变换动画 -->
                <div class="animation-card" onclick="playAnimation('SimpleGeometry')">
                    <div class="animation-preview">
                        <video id="video-SimpleGeometry" preload="metadata" muted>
                            <source src="../media/videos/simple_geometry/480p15/SimpleGeometry.mp4" type="video/mp4">
                        </video>
                        <button class="play-overlay">▶</button>
                    </div>
                    <div class="animation-title">几何变换动画</div>
                    <div class="animation-description">展示平移、旋转、缩放等基本几何变换，帮助理解图形变化规律。</div>
                    <div class="animation-meta">
                        <span>几何 • 基础</span>
                        <span class="animation-duration">~25秒</span>
                    </div>
                </div>

                <!-- 集成测试动画 -->
                <div class="animation-card" onclick="playAnimation('IntegrationTest')">
                    <div class="animation-preview">
                        <video id="video-IntegrationTest" preload="metadata" muted>
                            <source src="../media/videos/test_integration/480p15/IntegrationTest.mp4" type="video/mp4">
                        </video>
                        <button class="play-overlay">▶</button>
                    </div>
                    <div class="animation-title">系统集成测试</div>
                    <div class="animation-description">完整的系统功能验证，展示项目各组件的协同工作效果。</div>
                    <div class="animation-meta">
                        <span>系统 • 演示</span>
                        <span class="animation-duration">~30秒</span>
                    </div>
                </div>

                <!-- 快速演示动画 -->
                <div class="animation-card" onclick="playAnimation('QuickDemo')">
                    <div class="animation-preview">
                        <video id="video-QuickDemo" preload="metadata" muted>
                            <source src="../media/videos/test_integration/480p15/QuickDemo.mp4" type="video/mp4">
                        </video>
                        <button class="play-overlay">▶</button>
                    </div>
                    <div class="animation-title">快速演示动画</div>
                    <div class="animation-description">展示多种几何图形的创建、变换和动画效果的综合演示。</div>
                    <div class="animation-meta">
                        <span>综合 • 演示</span>
                        <span class="animation-duration">~15秒</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="next-steps">
            <h3>🚀 下一步计划</h3>
            <p>
                1. 完善后端API接口<br>
                2. 开发前端React组件<br>
                3. 集成用户认证系统<br>
                4. 实现动画播放功能<br>
                5. 添加更多数学案例
            </p>
        </div>

        <div style="margin-top: 30px;">
            <a href="#" class="btn" onclick="showAnimations()">观看数学动画</a>
            <a href="#" class="btn btn-secondary" onclick="showDocs()">查看项目文档</a>
        </div>

        <div style="margin-top: 20px; color: #666; font-size: 0.9rem;">
            <p>技术栈：React + Spring Boot + PostgreSQL + Manim</p>
            <p>版本：v1.0.0-dev</p>
        </div>
    </div>

    <script>
        // 动画播放功能
        function playAnimation(animationId) {
            const video = document.getElementById('video-' + animationId);
            const overlay = video.parentElement.querySelector('.play-overlay');

            if (video.paused) {
                // 暂停其他所有视频
                document.querySelectorAll('video').forEach(v => {
                    if (v !== video) {
                        v.pause();
                        v.parentElement.querySelector('.play-overlay').style.display = 'block';
                        v.parentElement.querySelector('.play-overlay').innerHTML = '▶';
                    }
                });

                // 播放当前视频
                video.play().then(() => {
                    overlay.style.display = 'none';
                }).catch(error => {
                    console.error('播放失败:', error);
                    alert('视频播放失败，请检查文件路径是否正确。\n\n' +
                          '确保动画文件存在于以下路径：\n' +
                          '../media/videos/[相应目录]/[动画文件].mp4');
                });
            } else {
                // 暂停视频
                video.pause();
                overlay.style.display = 'block';
                overlay.innerHTML = '▶';
            }
        }

        // 视频播放结束事件
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('video').forEach(video => {
                video.addEventListener('ended', function() {
                    const overlay = this.parentElement.querySelector('.play-overlay');
                    overlay.style.display = 'block';
                    overlay.innerHTML = '🔄';

                    // 3秒后重置播放按钮
                    setTimeout(() => {
                        overlay.innerHTML = '▶';
                    }, 3000);
                });

                video.addEventListener('pause', function() {
                    const overlay = this.parentElement.querySelector('.play-overlay');
                    overlay.style.display = 'block';
                    overlay.innerHTML = '▶';
                });

                video.addEventListener('play', function() {
                    const overlay = this.parentElement.querySelector('.play-overlay');
                    overlay.style.display = 'none';
                });

                // 视频加载错误处理
                video.addEventListener('error', function() {
                    const overlay = this.parentElement.querySelector('.play-overlay');
                    overlay.innerHTML = '❌';
                    overlay.style.background = 'rgba(220, 53, 69, 0.8)';
                    console.error('视频加载失败:', this.src);
                });
            });
        });

        function showAnimations() {
            // 滚动到动画区域
            document.querySelector('.animations-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        function showDocs() {
            alert('项目文档：\n\n' +
                  '• README.md - 项目说明\n' +
                  '• QUICK_START.md - 快速启动指南\n' +
                  '• DEVELOPMENT_LOG.md - 开发日志\n' +
                  '• PROJECT_SUMMARY.md - 项目总结\n' +
                  '• TEST_REPORT.md - 测试报告\n' +
                  '• math-web.md - 详细架构文档');
        }

        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    feature.style.transition = 'all 0.5s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
