"""
集成测试动画
验证整个系统的工作流程
"""

from manim import *

class IntegrationTest(Scene):
    def construct(self):
        # 欢迎标题
        title = Text("数学家-WEB 集成测试", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)

        # 系统状态检查
        status_title = Text("系统状态检查", font_size=32, color=GREEN)
        status_title.next_to(title, DOWN, buff=1)
        self.play(Write(status_title))
        self.wait(1)

        # 检查项目列表
        checks = [
            "✅ Manim动画引擎",
            "✅ Python环境配置", 
            "✅ 前端页面创建",
            "✅ 后端框架搭建",
            "✅ 项目文档完整"
        ]

        check_group = VGroup()
        for i, check in enumerate(checks):
            check_text = Text(check, font_size=24, color=WHITE)
            check_text.shift(UP * (2 - i * 0.8))
            check_group.add(check_text)
            self.play(Write(check_text), run_time=0.5)
            self.wait(0.3)

        self.wait(2)

        # 清除检查列表
        self.play(FadeOut(check_group), FadeOut(status_title))

        # 展示核心功能
        demo_title = Text("核心功能演示", font_size=32, color=YELLOW)
        demo_title.next_to(title, DOWN, buff=1)
        self.play(Write(demo_title))
        self.wait(1)

        # 创建坐标系
        axes = Axes(
            x_range=[-3, 3, 1],
            y_range=[-3, 3, 1],
            x_length=6,
            y_length=4,
            axis_config={"color": WHITE, "include_numbers": False},
        )
        
        self.play(Create(axes))
        self.wait(1)

        # 动态函数演示
        def func(x):
            return x**2

        graph = axes.plot(func, color=RED, x_range=[-2, 2])
        func_label = Text("y = x²", font_size=24, color=RED)
        func_label.to_corner(UL)
        
        self.play(Create(graph), Write(func_label))
        self.wait(1)

        # 添加动态点
        dot = Dot(color=ORANGE, radius=0.08)
        x_tracker = ValueTracker(-2)
        
        def update_dot(mob):
            x = x_tracker.get_value()
            y = func(x)
            mob.move_to(axes.coords_to_point(x, y))
        
        dot.add_updater(update_dot)
        self.add(dot)
        
        # 动画移动点
        self.play(x_tracker.animate.set_value(2), run_time=3)
        self.wait(1)
        
        dot.clear_updaters()

        # 清除演示
        self.play(FadeOut(Group(axes, graph, func_label, dot, demo_title)))

        # 成功消息
        success_msg = Text("🎉 集成测试成功！", font_size=36, color=GREEN)
        success_msg.next_to(title, DOWN, buff=2)
        self.play(Write(success_msg))
        self.wait(1)

        # 项目信息
        info_lines = [
            "项目：数学家-WEB",
            "状态：开发中",
            "技术栈：React + Spring Boot + Manim",
            "版本：v1.0.0-dev"
        ]

        info_group = VGroup()
        for i, line in enumerate(info_lines):
            info_text = Text(line, font_size=20, color=WHITE)
            info_text.shift(DOWN * (0.5 + i * 0.5))
            info_group.add(info_text)
            self.play(Write(info_text), run_time=0.3)

        self.wait(3)

        # 结束动画
        self.play(FadeOut(Group(*self.mobjects)))
        
        end_text = Text("准备开始数学学习之旅！", font_size=32, color=BLUE)
        self.play(Write(end_text))
        self.wait(2)


class QuickDemo(Scene):
    """
    快速演示场景
    """
    def construct(self):
        # 创建标题
        title = Text("数学动画演示", font_size=40, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        
        # 移动标题到顶部
        self.play(title.animate.to_edge(UP))
        
        # 创建几个几何图形
        circle = Circle(radius=1, color=RED, fill_opacity=0.3)
        square = Square(side_length=1.5, color=GREEN, fill_opacity=0.3)
        triangle = Triangle(color=YELLOW, fill_opacity=0.3)
        
        # 排列图形
        shapes = VGroup(circle, square, triangle)
        shapes.arrange(RIGHT, buff=1)
        
        # 依次创建图形
        for shape in shapes:
            self.play(Create(shape))
            self.wait(0.5)
        
        # 同时变换所有图形
        self.play(
            circle.animate.scale(1.5),
            square.animate.rotate(PI/4),
            triangle.animate.shift(UP),
            run_time=2
        )
        self.wait(1)
        
        # 改变颜色
        self.play(
            circle.animate.set_color(PURPLE),
            square.animate.set_color(ORANGE),
            triangle.animate.set_color(PINK),
            run_time=1
        )
        self.wait(1)
        
        # 结束
        self.play(FadeOut(Group(*self.mobjects)))
        
        final_text = Text("数学让世界更美好！", font_size=32, color=GOLD)
        self.play(Write(final_text))
        self.wait(2)
