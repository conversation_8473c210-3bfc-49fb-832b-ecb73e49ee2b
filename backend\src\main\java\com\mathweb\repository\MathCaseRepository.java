package com.mathweb.repository;

import com.mathweb.entity.MathCase;
import com.mathweb.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 数学案例数据访问接口
 * 
 * <AUTHOR> Animator Team
 */
@Repository
public interface MathCaseRepository extends JpaRepository<MathCase, Long> {

    /**
     * 查找活跃的案例
     */
    List<MathCase> findByIsActiveTrueOrderByCreatedAtDesc();

    /**
     * 分页查找活跃的案例
     */
    Page<MathCase> findByIsActiveTrue(Pageable pageable);

    /**
     * 根据难度级别查找案例
     */
    List<MathCase> findByLevelAndIsActiveTrueOrderByViewCountDesc(MathCase.CaseLevel level);

    /**
     * 根据难度级别分页查找案例
     */
    Page<MathCase> findByLevelAndIsActiveTrue(MathCase.CaseLevel level, Pageable pageable);

    /**
     * 根据分类查找案例
     */
    List<MathCase> findByCategoryAndIsActiveTrueOrderByCreatedAtDesc(MathCase.CaseCategory category);

    /**
     * 根据分类分页查找案例
     */
    Page<MathCase> findByCategoryAndIsActiveTrue(MathCase.CaseCategory category, Pageable pageable);

    /**
     * 根据难度级别和分类查找案例
     */
    Page<MathCase> findByLevelAndCategoryAndIsActiveTrue(
            MathCase.CaseLevel level, 
            MathCase.CaseCategory category, 
            Pageable pageable);

    /**
     * 根据创建者查找案例
     */
    Page<MathCase> findByCreatedBy(User createdBy, Pageable pageable);

    /**
     * 根据标题模糊查找案例
     */
    Page<MathCase> findByTitleContainingIgnoreCaseAndIsActiveTrue(String title, Pageable pageable);

    /**
     * 根据描述模糊查找案例
     */
    Page<MathCase> findByDescriptionContainingIgnoreCaseAndIsActiveTrue(String description, Pageable pageable);

    /**
     * 根据标题或描述模糊查找案例
     */
    @Query("SELECT mc FROM MathCase mc WHERE mc.isActive = true AND " +
           "(LOWER(mc.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(mc.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<MathCase> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找热门案例（按观看次数排序）
     */
    @Query("SELECT mc FROM MathCase mc WHERE mc.isActive = true ORDER BY mc.viewCount DESC")
    Page<MathCase> findPopularCases(Pageable pageable);

    /**
     * 查找最新案例
     */
    @Query("SELECT mc FROM MathCase mc WHERE mc.isActive = true ORDER BY mc.createdAt DESC")
    Page<MathCase> findLatestCases(Pageable pageable);

    /**
     * 根据用户角色查找可访问的案例
     */
    @Query("SELECT mc FROM MathCase mc WHERE mc.isActive = true AND " +
           "(:userRole = 'ADMIN' OR " +
           "(:userRole = 'TEACHER' AND mc.level IN ('BASIC', 'INTERMEDIATE')) OR " +
           "(:userRole = 'STUDENT' AND mc.level = 'BASIC'))")
    Page<MathCase> findAccessibleCases(@Param("userRole") String userRole, Pageable pageable);

    /**
     * 增加案例观看次数
     */
    @Modifying
    @Query("UPDATE MathCase mc SET mc.viewCount = mc.viewCount + 1 WHERE mc.id = :caseId")
    void incrementViewCount(@Param("caseId") Long caseId);

    /**
     * 更新案例活跃状态
     */
    @Modifying
    @Query("UPDATE MathCase mc SET mc.isActive = :active WHERE mc.id = :caseId")
    void updateActiveStatus(@Param("caseId") Long caseId, @Param("active") Boolean active);

    /**
     * 统计各分类案例数量
     */
    @Query("SELECT mc.category, COUNT(mc) FROM MathCase mc WHERE mc.isActive = true GROUP BY mc.category")
    List<Object[]> countCasesByCategory();

    /**
     * 统计各难度级别案例数量
     */
    @Query("SELECT mc.level, COUNT(mc) FROM MathCase mc WHERE mc.isActive = true GROUP BY mc.level")
    List<Object[]> countCasesByLevel();

    /**
     * 统计总观看次数
     */
    @Query("SELECT SUM(mc.viewCount) FROM MathCase mc WHERE mc.isActive = true")
    Long getTotalViewCount();

    /**
     * 查找指定时间之后创建的案例
     */
    List<MathCase> findByCreatedAtAfterAndIsActiveTrue(LocalDateTime dateTime);

    /**
     * 多条件搜索案例
     */
    @Query("SELECT mc FROM MathCase mc WHERE mc.isActive = true AND " +
           "(:title IS NULL OR LOWER(mc.title) LIKE LOWER(CONCAT('%', :title, '%'))) AND " +
           "(:category IS NULL OR mc.category = :category) AND " +
           "(:level IS NULL OR mc.level = :level) AND " +
           "(:createdBy IS NULL OR mc.createdBy = :createdBy)")
    Page<MathCase> searchCases(@Param("title") String title,
                              @Param("category") MathCase.CaseCategory category,
                              @Param("level") MathCase.CaseLevel level,
                              @Param("createdBy") User createdBy,
                              Pageable pageable);

    /**
     * 查找推荐案例（基于观看次数和创建时间的综合排序）
     */
    @Query("SELECT mc FROM MathCase mc WHERE mc.isActive = true " +
           "ORDER BY (mc.viewCount * 0.7 + EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - mc.createdAt)) / 86400 * 0.3) DESC")
    Page<MathCase> findRecommendedCases(Pageable pageable);

    /**
     * 根据脚本路径查找案例
     */
    Optional<MathCase> findByScriptPath(String scriptPath);

    /**
     * 检查脚本路径是否已存在
     */
    boolean existsByScriptPath(String scriptPath);
}
