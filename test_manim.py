"""
简单的Manim测试脚本
用于验证Manim环境是否正常工作
"""

from manim import *

class SimpleTest(Scene):
    def construct(self):
        # 创建一个简单的文本
        text = Text("Hello, Math World!", font_size=48, color=BLUE)
        self.play(Write(text))
        self.wait(1)
        
        # 创建一个圆
        circle = Circle(radius=2, color=RED)
        circle.next_to(text, DOWN, buff=1)
        self.play(Create(circle))
        self.wait(1)
        
        # 让圆变换颜色
        self.play(circle.animate.set_color(GREEN))
        self.wait(1)
        
        # 结束
        self.play(FadeOut(text), FadeOut(circle))
        self.wait(1)

if __name__ == "__main__":
    # 直接运行测试
    print("Testing Manim...")
    try:
        from manim import config
        config.media_dir = "./media"
        scene = SimpleTest()
        scene.render()
        print("Manim test completed successfully!")
    except Exception as e:
        print(f"Manim test failed: {e}")
