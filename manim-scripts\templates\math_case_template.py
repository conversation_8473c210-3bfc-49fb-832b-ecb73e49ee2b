"""
数学案例Manim脚本模板
用于快速创建新的数学动画案例

使用说明：
1. 复制此模板文件
2. 修改类名和文件名
3. 在construct方法中编写动画逻辑
4. 更新案例信息注释
"""

from manim import *

class MathCaseTemplate(Scene):
    """
    数学案例模板类
    
    案例信息：
    - 标题: [在此填写案例标题]
    - 描述: [在此填写案例描述]
    - 难度: [BASIC/INTERMEDIATE/ADVANCED]
    - 分类: [GEOMETRY/ALGEBRA/FUNCTION等]
    - 时长: [预计动画时长，秒]
    """
    
    def construct(self):
        """
        动画构建方法
        在此方法中编写动画逻辑
        """
        
        # 1. 标题和介绍
        title = Text("[案例标题]", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 2. 主要内容区域
        # 在此添加主要的数学概念展示
        
        # 示例：创建坐标系
        axes = self.create_axes()
        self.play(Create(axes))
        self.wait(1)
        
        # 示例：添加数学对象
        math_object = self.create_math_object()
        self.play(Create(math_object))
        self.wait(1)
        
        # 3. 动画演示
        # 在此添加动画变换和演示
        self.demonstrate_concept(math_object)
        
        # 4. 总结和结论
        self.show_conclusion()
        
        # 5. 结束
        self.end_animation()
    
    def create_axes(self):
        """
        创建坐标系
        根据需要调整坐标系的范围和样式
        """
        axes = Axes(
            x_range=[-5, 5, 1],
            y_range=[-5, 5, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE},
            tips=False,
        )
        axes_labels = axes.get_axis_labels(x_label="x", y_label="y")
        return VGroup(axes, axes_labels)
    
    def create_math_object(self):
        """
        创建主要的数学对象
        根据案例需要创建相应的几何图形、函数图像等
        """
        # 示例：创建一个圆
        circle = Circle(radius=2, color=RED, fill_opacity=0.3)
        return circle
    
    def demonstrate_concept(self, math_object):
        """
        演示数学概念
        
        Args:
            math_object: 要演示的数学对象
        """
        # 示例：对象的变换动画
        self.play(
            math_object.animate.scale(1.5),
            run_time=2
        )
        self.wait(1)
        
        self.play(
            math_object.animate.shift(RIGHT * 2),
            run_time=2
        )
        self.wait(1)
    
    def show_conclusion(self):
        """
        显示总结和结论
        """
        conclusion = Text(
            "[在此添加总结文字]",
            font_size=24,
            color=YELLOW
        )
        conclusion.to_edge(DOWN)
        self.play(Write(conclusion))
        self.wait(2)
    
    def end_animation(self):
        """
        结束动画
        """
        # 淡出所有对象
        self.play(FadeOut(Group(*self.mobjects)))
        
        # 显示结束文字
        end_text = Text("感谢观看！", font_size=36, color=GREEN)
        self.play(Write(end_text))
        self.wait(2)


class InteractiveMathCase(Scene):
    """
    交互式数学案例模板
    用于创建可以通过参数控制的动画
    """
    
    def construct(self):
        # 创建参数跟踪器
        parameter = ValueTracker(1.0)
        
        # 创建动态对象
        dynamic_object = always_redraw(
            lambda: self.create_dynamic_object(parameter.get_value())
        )
        
        # 创建参数显示
        parameter_display = always_redraw(
            lambda: Text(
                f"参数值: {parameter.get_value():.2f}",
                font_size=24
            ).to_corner(UL)
        )
        
        self.add(dynamic_object, parameter_display)
        
        # 演示参数变化
        parameter_values = [0.5, 2.0, -1.0, 1.5, 1.0]
        for value in parameter_values:
            self.play(parameter.animate.set_value(value), run_time=2)
            self.wait(1)
    
    def create_dynamic_object(self, param_value):
        """
        根据参数值创建动态对象
        
        Args:
            param_value: 参数值
            
        Returns:
            动态创建的Manim对象
        """
        # 示例：根据参数创建不同大小的圆
        circle = Circle(radius=abs(param_value), color=BLUE)
        if param_value < 0:
            circle.set_color(RED)
        return circle


# 工具函数
def create_math_text(text, position=ORIGIN, color=WHITE, font_size=24):
    """
    创建数学文本的工具函数
    
    Args:
        text: 文本内容
        position: 位置
        color: 颜色
        font_size: 字体大小
        
    Returns:
        Text对象
    """
    math_text = Text(text, font_size=font_size, color=color)
    math_text.move_to(position)
    return math_text

def create_equation(latex_string, position=ORIGIN, color=WHITE):
    """
    创建数学公式的工具函数
    
    Args:
        latex_string: LaTeX格式的公式字符串
        position: 位置
        color: 颜色
        
    Returns:
        MathTex对象
    """
    equation = MathTex(latex_string, color=color)
    equation.move_to(position)
    return equation

def highlight_object(obj, color=YELLOW, scale_factor=1.1, duration=1):
    """
    高亮显示对象的工具函数
    
    Args:
        obj: 要高亮的对象
        color: 高亮颜色
        scale_factor: 缩放因子
        duration: 动画时长
        
    Returns:
        动画序列
    """
    original_color = obj.get_color()
    return [
        obj.animate.set_color(color).scale(scale_factor),
        Wait(duration),
        obj.animate.set_color(original_color).scale(1/scale_factor)
    ]

# 常用配置
MATH_COLORS = {
    'primary': BLUE,
    'secondary': RED,
    'accent': YELLOW,
    'success': GREEN,
    'warning': ORANGE,
    'info': TEAL,
    'text': WHITE
}

ANIMATION_CONFIG = {
    'slow': 3,
    'normal': 2,
    'fast': 1,
    'very_fast': 0.5
}
