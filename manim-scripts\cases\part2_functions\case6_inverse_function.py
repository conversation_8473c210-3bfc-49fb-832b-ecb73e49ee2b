"""
案例6：反比例函数的双曲线
知识要点：反比例函数的图像特征、渐近线
难度等级：⭐⭐⭐
"""

from manim import *
import numpy as np

class InverseFunction(Scene):
    def construct(self):
        # 标题
        title = Text("案例6：反比例函数的双曲线", font_size=36, color=BLUE)
        subtitle = Text("y = k/x 的图像特征与渐近线", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.3)
        
        self.play(Write(title_group))
        self.wait(1)
        self.play(title_group.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[-4, 4, 1],
            x_length=8,
            y_length=8,
            axis_config={"color": WHITE, "stroke_width": 2},
            tips=True
        )
        
        x_label = axes.get_x_axis_label("x")
        y_label = axes.get_y_axis_label("y")
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 1. 基础反比例函数 y = 1/x
        basic_text = Text("1. 基础反比例函数 y = 1/x", font_size=24, color=YELLOW)
        basic_text.to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        self.play(Write(basic_text))
        
        # 绘制双曲线（分两支）
        k = 1
        
        # 右支 (x > 0)
        hyperbola_right = axes.plot(
            lambda x: k / x, 
            x_range=[0.3, 4], 
            color=BLUE, 
            stroke_width=3,
            discontinuities=[0]
        )
        
        # 左支 (x < 0)  
        hyperbola_left = axes.plot(
            lambda x: k / x,
            x_range=[-4, -0.3],
            color=BLUE,
            stroke_width=3,
            discontinuities=[0]
        )
        
        equation = MathTex("y = \\frac{1}{x}", font_size=24, color=BLUE)
        equation.to_edge(RIGHT, buff=0.5).shift(UP*2)
        
        self.play(Create(hyperbola_right), Create(hyperbola_left), Write(equation))
        self.wait(2)
        
        # 2. 渐近线的几何意义
        self.play(basic_text.animate.become(
            Text("2. 渐近线的几何意义", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # x轴渐近线
        x_asymptote = DashedLine(
            axes.coords_to_point(-4, 0),
            axes.coords_to_point(4, 0),
            color=RED, stroke_width=2
        )
        
        # y轴渐近线
        y_asymptote = DashedLine(
            axes.coords_to_point(0, -4),
            axes.coords_to_point(0, 4),
            color=RED, stroke_width=2
        )
        
        asymptote_label_x = Text("x轴: y = 0", font_size=16, color=RED)
        asymptote_label_x.to_edge(RIGHT, buff=0.5).shift(UP*1.5)
        
        asymptote_label_y = Text("y轴: x = 0", font_size=16, color=RED)
        asymptote_label_y.to_edge(RIGHT, buff=0.5).shift(UP*1)
        
        self.play(Create(x_asymptote), Create(y_asymptote))
        self.play(Write(asymptote_label_x), Write(asymptote_label_y))
        self.wait(2)
        
        # 3. k值变化对双曲线的影响
        self.play(basic_text.animate.become(
            Text("3. k值变化对双曲线的影响", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 创建动态k值
        k_tracker = ValueTracker(1)
        
        dynamic_hyperbola_right = always_redraw(lambda:
            axes.plot(
                lambda x: k_tracker.get_value() / x,
                x_range=[0.3, 4],
                color=BLUE,
                stroke_width=3,
                discontinuities=[0]
            )
        )
        
        dynamic_hyperbola_left = always_redraw(lambda:
            axes.plot(
                lambda x: k_tracker.get_value() / x,
                x_range=[-4, -0.3],
                color=BLUE,
                stroke_width=3,
                discontinuities=[0]
            )
        )
        
        dynamic_equation = always_redraw(lambda:
            MathTex(f"y = \\frac{{{k_tracker.get_value():.1f}}}{{x}}", 
                   font_size=24, color=BLUE)
            .to_edge(RIGHT, buff=0.5).shift(UP*2)
        )
        
        self.remove(hyperbola_right, hyperbola_left, equation)
        self.add(dynamic_hyperbola_right, dynamic_hyperbola_left, dynamic_equation)
        
        # 改变k值
        k_values = [2, 4, -2, -1, 1]
        for k_val in k_values:
            self.play(k_tracker.animate.set_value(k_val), run_time=2)
            self.wait(1)
        
        # 4. 象限分布规律
        self.play(basic_text.animate.become(
            Text("4. 象限分布规律", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 设置k为正值
        k_tracker.set_value(2)
        
        # 标记象限
        quadrant_labels = VGroup(
            Text("第一象限", font_size=14, color=GREEN).move_to(axes.coords_to_point(2, 2)),
            Text("第二象限", font_size=14, color=GRAY).move_to(axes.coords_to_point(-2, 2)),
            Text("第三象限", font_size=14, color=GREEN).move_to(axes.coords_to_point(-2, -2)),
            Text("第四象限", font_size=14, color=GRAY).move_to(axes.coords_to_point(2, -2))
        )
        
        rule_text = Text("k>0: 第一、三象限", font_size=18, color=GREEN)
        rule_text.to_edge(RIGHT, buff=0.5).shift(DOWN*0.5)
        
        self.play(Write(quadrant_labels), Write(rule_text))
        self.wait(2)
        
        # 改变为负值
        self.play(k_tracker.animate.set_value(-2), run_time=2)
        
        # 更新象限标记
        new_quadrant_labels = VGroup(
            Text("第一象限", font_size=14, color=GRAY).move_to(axes.coords_to_point(2, 2)),
            Text("第二象限", font_size=14, color=GREEN).move_to(axes.coords_to_point(-2, 2)),
            Text("第三象限", font_size=14, color=GRAY).move_to(axes.coords_to_point(-2, -2)),
            Text("第四象限", font_size=14, color=GREEN).move_to(axes.coords_to_point(2, -2))
        )
        
        new_rule_text = Text("k<0: 第二、四象限", font_size=18, color=GREEN)
        new_rule_text.to_edge(RIGHT, buff=0.5).shift(DOWN*0.5)
        
        self.play(Transform(quadrant_labels, new_quadrant_labels),
                 Transform(rule_text, new_rule_text))
        self.wait(2)
        
        # 5. 对称性质
        self.play(basic_text.animate.become(
            Text("5. 对称性质", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 设置k为正值演示对称性
        k_tracker.set_value(2)
        
        # 在双曲线上取对称点
        x_val = 1
        y_val = 2 / x_val
        
        point1 = Dot(axes.coords_to_point(x_val, y_val), color=ORANGE, radius=0.08)
        point2 = Dot(axes.coords_to_point(-x_val, -y_val), color=ORANGE, radius=0.08)
        
        label1 = Text(f"({x_val},{y_val})", font_size=14, color=ORANGE)
        label1.next_to(point1, UP+RIGHT, buff=0.1)
        
        label2 = Text(f"({-x_val},{-y_val})", font_size=14, color=ORANGE)
        label2.next_to(point2, DOWN+LEFT, buff=0.1)
        
        # 对称线
        symmetry_line = DashedLine(
            axes.coords_to_point(-3, -3),
            axes.coords_to_point(3, 3),
            color=ORANGE, stroke_width=2
        )
        
        symmetry_label = Text("关于原点对称", font_size=16, color=ORANGE)
        symmetry_label.to_edge(RIGHT, buff=0.5).shift(DOWN*1.5)
        
        self.play(Create(point1), Create(point2), Write(label1), Write(label2))
        self.play(Create(symmetry_line), Write(symmetry_label))
        self.wait(2)
        
        # 6. 实际应用示例
        self.play(basic_text.animate.become(
            Text("6. 实际应用：反比例关系", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 清除对称相关元素
        self.play(FadeOut(point1), FadeOut(point2), FadeOut(label1), FadeOut(label2),
                 FadeOut(symmetry_line), FadeOut(symmetry_label), FadeOut(quadrant_labels),
                 FadeOut(rule_text))
        
        # 应用示例
        application_text = VGroup(
            Text("应用实例：", font_size=18, color=WHITE),
            Text("• 速度与时间: v = s/t", font_size=16, color=CYAN),
            Text("• 压强与体积: P = k/V", font_size=16, color=CYAN),
            Text("• 电阻与电流: R = U/I", font_size=16, color=CYAN)
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.2)
        application_text.to_edge(LEFT, buff=0.5).shift(DOWN*1)
        
        self.play(Write(application_text))
        self.wait(3)
        
        # 总结
        conclusion = Text("反比例关系 ↔ 双曲线几何特征", font_size=28, color=GREEN)
        conclusion.to_edge(DOWN, buff=0.2)
        
        self.play(Write(conclusion))
        self.wait(2)
        
        # 淡出
        self.play(*[FadeOut(mob) for mob in self.mobjects])
        self.wait(1)

class InverseFunctionDemo(Scene):
    """简化版演示"""
    def construct(self):
        title = Text("反比例函数的双曲线", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 双曲线
        hyperbola_right = axes.plot(lambda x: 1/x, x_range=[0.3, 3], color=BLUE, stroke_width=3)
        hyperbola_left = axes.plot(lambda x: 1/x, x_range=[-3, -0.3], color=BLUE, stroke_width=3)
        
        self.play(Create(hyperbola_right), Create(hyperbola_left))
        
        # 方程
        equation = MathTex(r"y = \frac{k}{x}", font_size=24, color=BLUE)
        equation.to_edge(UR)
        self.play(Write(equation))
        
        # 渐近线
        x_asymptote = DashedLine(axes.coords_to_point(-3, 0), axes.coords_to_point(3, 0), color=RED)
        y_asymptote = DashedLine(axes.coords_to_point(0, -3), axes.coords_to_point(0, 3), color=RED)
        
        self.play(Create(x_asymptote), Create(y_asymptote))
        self.wait(3)
