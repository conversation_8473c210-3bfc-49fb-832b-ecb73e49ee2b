"""
案例7：三角形面积公式的推导
知识要点：三角形面积的多种计算方法
难度等级：⭐⭐⭐
"""

from manim import *
import numpy as np

class TriangleArea(Scene):
    def construct(self):
        # 标题
        title = Text("案例7：三角形面积公式的推导", font_size=36, color=BLUE)
        subtitle = Text("从矩形面积到三角形面积", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.3)
        
        self.play(Write(title_group))
        self.wait(1)
        self.play(title_group.animate.to_edge(UP))
        
        # 1. 从矩形面积到三角形面积的推导
        derivation_text = Text("1. 从矩形面积推导三角形面积", font_size=24, color=YELLOW)
        derivation_text.to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        self.play(Write(derivation_text))
        
        # 创建矩形
        rectangle = Rectangle(width=4, height=2.5, color=BLUE, stroke_width=3)
        rectangle.shift(LEFT*2)
        
        # 标记矩形的底和高
        base_label = Text("底 = b", font_size=18, color=BLUE)
        base_label.next_to(rectangle, DOWN, buff=0.2)
        
        height_label = Text("高 = h", font_size=18, color=BLUE)
        height_label.next_to(rectangle, LEFT, buff=0.2)
        
        # 矩形面积公式
        rect_area_formula = MathTex("S_{矩形} = b \\times h", font_size=24, color=BLUE)
        rect_area_formula.to_edge(RIGHT, buff=0.5).shift(UP*1)
        
        self.play(Create(rectangle), Write(base_label), Write(height_label))
        self.play(Write(rect_area_formula))
        self.wait(2)
        
        # 绘制对角线，将矩形分成两个三角形
        diagonal = Line(
            rectangle.get_corner(DOWN+LEFT),
            rectangle.get_corner(UP+RIGHT),
            color=RED, stroke_width=3
        )
        
        self.play(Create(diagonal))
        self.wait(1)
        
        # 标记两个三角形
        triangle1_label = Text("△1", font_size=16, color=GREEN)
        triangle1_label.move_to(rectangle.get_center() + UP*0.3 + LEFT*0.5)
        
        triangle2_label = Text("△2", font_size=16, color=GREEN)
        triangle2_label.move_to(rectangle.get_center() + DOWN*0.3 + RIGHT*0.5)
        
        self.play(Write(triangle1_label), Write(triangle2_label))
        self.wait(1)
        
        # 三角形面积公式推导
        triangle_area_formula = MathTex(
            "S_{三角形} = \\frac{1}{2} S_{矩形} = \\frac{1}{2} \\times b \\times h",
            font_size=20, color=GREEN
        )
        triangle_area_formula.to_edge(RIGHT, buff=0.5)
        
        self.play(Write(triangle_area_formula))
        self.wait(2)
        
        # 2. 坐标系中三角形面积的计算
        self.play(derivation_text.animate.become(
            Text("2. 坐标系中三角形面积", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 清除矩形相关元素
        self.play(FadeOut(rectangle), FadeOut(diagonal), FadeOut(base_label), 
                 FadeOut(height_label), FadeOut(triangle1_label), FadeOut(triangle2_label),
                 FadeOut(rect_area_formula), FadeOut(triangle_area_formula))
        
        # 创建坐标系
        axes = Axes(
            x_range=[-1, 4, 1],
            y_range=[-1, 3, 1],
            x_length=5,
            y_length=4,
            axis_config={"color": WHITE, "stroke_width": 2},
            tips=True
        )
        axes.shift(LEFT*1.5)
        
        x_label = axes.get_x_axis_label("x")
        y_label = axes.get_y_axis_label("y")
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        
        # 三角形的三个顶点
        A = np.array([1, 0.5])
        B = np.array([3, 0.5]) 
        C = np.array([2, 2.5])
        
        # 创建三角形
        triangle = Polygon(
            axes.coords_to_point(*A),
            axes.coords_to_point(*B),
            axes.coords_to_point(*C),
            color=GREEN, stroke_width=3, fill_opacity=0.3, fill_color=GREEN
        )
        
        # 标记顶点
        dot_A = Dot(axes.coords_to_point(*A), color=RED, radius=0.06)
        dot_B = Dot(axes.coords_to_point(*B), color=RED, radius=0.06)
        dot_C = Dot(axes.coords_to_point(*C), color=RED, radius=0.06)
        
        label_A = Text(f"A({A[0]},{A[1]})", font_size=14, color=RED)
        label_A.next_to(dot_A, DOWN+LEFT, buff=0.1)
        
        label_B = Text(f"B({B[0]},{B[1]})", font_size=14, color=RED)
        label_B.next_to(dot_B, DOWN+RIGHT, buff=0.1)
        
        label_C = Text(f"C({C[0]},{C[1]})", font_size=14, color=RED)
        label_C.next_to(dot_C, UP, buff=0.1)
        
        self.play(Create(triangle))
        self.play(Create(dot_A), Create(dot_B), Create(dot_C))
        self.play(Write(label_A), Write(label_B), Write(label_C))
        self.wait(1)
        
        # 坐标公式
        coordinate_formula = MathTex(
            "S = \\frac{1}{2}|x_1(y_2-y_3) + x_2(y_3-y_1) + x_3(y_1-y_2)|",
            font_size=16, color=GREEN
        )
        coordinate_formula.to_edge(RIGHT, buff=0.2).shift(UP*1)
        
        self.play(Write(coordinate_formula))
        
        # 具体计算
        calculation = MathTex(
            f"S = \\frac{{1}}{{2}}|{A[0]}({B[1]}-{C[1]}) + {B[0]}({C[1]}-{A[1]}) + {C[0]}({A[1]}-{B[1]})|",
            font_size=14, color=GREEN
        )
        calculation.to_edge(RIGHT, buff=0.2).shift(UP*0.3)
        
        # 计算结果
        area_value = 0.5 * abs(A[0]*(B[1]-C[1]) + B[0]*(C[1]-A[1]) + C[0]*(A[1]-B[1]))
        result = MathTex(f"S = {area_value}", font_size=16, color=GREEN)
        result.to_edge(RIGHT, buff=0.2).shift(DOWN*0.3)
        
        self.play(Write(calculation))
        self.wait(1)
        self.play(Write(result))
        self.wait(2)
        
        # 3. 海伦公式
        self.play(derivation_text.animate.become(
            Text("3. 海伦公式", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 标记三边长
        side_a = Line(axes.coords_to_point(*B), axes.coords_to_point(*C), color=ORANGE, stroke_width=2)
        side_b = Line(axes.coords_to_point(*A), axes.coords_to_point(*C), color=ORANGE, stroke_width=2)
        side_c = Line(axes.coords_to_point(*A), axes.coords_to_point(*B), color=ORANGE, stroke_width=2)
        
        # 计算边长
        a = np.sqrt((B[0]-C[0])**2 + (B[1]-C[1])**2)
        b = np.sqrt((A[0]-C[0])**2 + (A[1]-C[1])**2)
        c = np.sqrt((A[0]-B[0])**2 + (A[1]-B[1])**2)
        
        label_a = Text(f"a={a:.1f}", font_size=12, color=ORANGE)
        label_a.move_to((axes.coords_to_point(*B) + axes.coords_to_point(*C))/2 + RIGHT*0.3)
        
        label_b = Text(f"b={b:.1f}", font_size=12, color=ORANGE)
        label_b.move_to((axes.coords_to_point(*A) + axes.coords_to_point(*C))/2 + LEFT*0.3)
        
        label_c = Text(f"c={c:.1f}", font_size=12, color=ORANGE)
        label_c.move_to((axes.coords_to_point(*A) + axes.coords_to_point(*B))/2 + DOWN*0.3)
        
        self.play(Create(side_a), Create(side_b), Create(side_c))
        self.play(Write(label_a), Write(label_b), Write(label_c))
        self.wait(1)
        
        # 海伦公式
        heron_formula = MathTex(
            "S = \\sqrt{p(p-a)(p-b)(p-c)}",
            font_size=20, color=PURPLE
        )
        heron_formula.to_edge(RIGHT, buff=0.2).shift(DOWN*1.2)
        
        # 半周长
        p = (a + b + c) / 2
        semi_perimeter = MathTex(f"p = \\frac{{a+b+c}}{{2}} = {p:.1f}", font_size=16, color=PURPLE)
        semi_perimeter.to_edge(RIGHT, buff=0.2).shift(DOWN*1.8)
        
        self.play(Write(heron_formula))
        self.play(Write(semi_perimeter))
        self.wait(2)
        
        # 4. 不同公式的应用场景
        self.play(derivation_text.animate.become(
            Text("4. 不同公式的应用场景", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 应用场景说明
        applications = VGroup(
            Text("• 底高公式: 已知底和高", font_size=16, color=WHITE),
            Text("• 坐标公式: 已知三个顶点坐标", font_size=16, color=WHITE),
            Text("• 海伦公式: 已知三边长", font_size=16, color=WHITE)
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.3)
        applications.to_edge(LEFT, buff=0.5).shift(DOWN*2)
        
        self.play(Write(applications))
        self.wait(3)
        
        # 总结
        conclusion = Text("几何面积 ↔ 代数公式", font_size=28, color=GREEN)
        conclusion.to_edge(DOWN, buff=0.2)
        
        self.play(Write(conclusion))
        self.wait(2)
        
        # 淡出
        self.play(*[FadeOut(mob) for mob in self.mobjects])
        self.wait(1)

class TriangleAreaDemo(Scene):
    """简化版演示"""
    def construct(self):
        title = Text("三角形面积公式", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 矩形到三角形
        rectangle = Rectangle(width=3, height=2, color=BLUE, stroke_width=3)
        self.play(Create(rectangle))
        
        # 对角线
        diagonal = Line(rectangle.get_corner(DL), rectangle.get_corner(UR), color=RED, stroke_width=3)
        self.play(Create(diagonal))
        
        # 公式
        formula = MathTex(r"S = \frac{1}{2} \times \text{底} \times \text{高}", font_size=24, color=GREEN)
        formula.to_edge(DOWN)
        self.play(Write(formula))
        
        self.wait(2)
        
        # 坐标公式
        coord_formula = MathTex(
            r"S = \frac{1}{2}|x_1(y_2-y_3) + x_2(y_3-y_1) + x_3(y_1-y_2)|",
            font_size=20, color=PURPLE
        )
        coord_formula.next_to(formula, UP)
        self.play(Write(coord_formula))
        
        self.wait(3)
