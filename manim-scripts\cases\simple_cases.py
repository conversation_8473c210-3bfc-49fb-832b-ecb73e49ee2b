"""
简化版数学动画案例 - 不依赖LaTeX
"""

from manim import *
import numpy as np

class Case1CoordinatePoints(Scene):
    """案例1：坐标系中的点"""
    def construct(self):
        # 标题
        title = Text("坐标系中的点", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 两个点
        A = Dot(axes.coords_to_point(2, 1), color=BLUE, radius=0.1)
        B = Dot(axes.coords_to_point(-1, 2), color=RED, radius=0.1)
        
        A_label = Text("A(2,1)", font_size=16, color=BLUE).next_to(A, UR)
        B_label = Text("B(-1,2)", font_size=16, color=RED).next_to(B, UL)
        
        self.play(Create(A), Create(B), Write(A_label), Write(B_label))
        
        # 距离线
        line = Line(A.get_center(), B.get_center(), color=YELLOW, stroke_width=3)
        self.play(Create(line))
        
        # 距离公式（使用Text而不是MathTex）
        formula = Text("距离公式: d = √[(x₂-x₁)² + (y₂-y₁)²]", font_size=20, color=GREEN)
        formula.to_edge(DOWN)
        self.play(Write(formula))
        
        self.wait(3)

class Case2LineEquations(Scene):
    """案例2：直线的方程"""
    def construct(self):
        title = Text("直线的方程", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 直线
        line = axes.plot(lambda x: 0.5 * x + 1, color=BLUE, stroke_width=3)
        self.play(Create(line))
        
        # 方程形式
        eq1 = Text("y = kx + b", font_size=20, color=YELLOW).to_edge(UR)
        eq2 = Text("点斜式: y - y₁ = k(x - x₁)", font_size=16, color=GREEN).next_to(eq1, DOWN)
        eq3 = Text("一般式: Ax + By + C = 0", font_size=16, color=RED).next_to(eq2, DOWN)
        
        self.play(Write(eq1))
        self.wait(1)
        self.play(Write(eq2))
        self.wait(1)
        self.play(Write(eq3))
        self.wait(2)

class Case3CircleEquations(Scene):
    """案例3：圆的方程"""
    def construct(self):
        title = Text("圆的方程", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 圆心和圆
        center = Dot(axes.coords_to_point(1, 0), color=RED, radius=0.08)
        circle = Circle(radius=1.5, color=BLUE, stroke_width=3).move_to(center.get_center())
        
        self.play(Create(center), Create(circle))
        
        # 方程
        equation = Text("标准方程: (x-a)² + (y-b)² = r²", font_size=20, color=BLUE)
        equation.to_edge(DOWN)
        self.play(Write(equation))
        
        # 半径线
        radius_line = Line(center.get_center(), 
                          center.get_center() + RIGHT*1.5, 
                          color=GREEN, stroke_width=2)
        radius_label = Text("r", font_size=20, color=GREEN).next_to(radius_line, UP)
        
        self.play(Create(radius_line), Write(radius_label))
        self.wait(3)

class Case4LinearFunction(Scene):
    """案例4：一次函数图像变换"""
    def construct(self):
        title = Text("一次函数图像变换", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 基础直线 y = x
        line1 = axes.plot(lambda x: x, color=BLUE, stroke_width=3)
        eq1 = Text("y = x", font_size=20, color=BLUE).to_edge(UR)
        
        self.play(Create(line1), Write(eq1))
        self.wait(1)
        
        # 变换为 y = x + 1
        line2 = axes.plot(lambda x: x + 1, color=GREEN, stroke_width=3)
        eq2 = Text("y = x + 1", font_size=20, color=GREEN).next_to(eq1, DOWN)
        
        self.play(Transform(line1, line2), Write(eq2))
        self.wait(1)
        
        # 变换为 y = 2x + 1
        line3 = axes.plot(lambda x: 2*x + 1, color=RED, stroke_width=3)
        eq3 = Text("y = 2x + 1", font_size=20, color=RED).next_to(eq2, DOWN)
        
        self.play(Transform(line1, line3), Write(eq3))
        self.wait(2)

class Case5QuadraticFunction(Scene):
    """案例5：二次函数的图像性质"""
    def construct(self):
        title = Text("二次函数图像性质", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-1, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 基础抛物线
        parabola1 = axes.plot(lambda x: x**2, color=BLUE, stroke_width=3)
        eq1 = Text("y = x²", font_size=20, color=BLUE).to_edge(UR)
        
        self.play(Create(parabola1), Write(eq1))
        self.wait(1)
        
        # 变换抛物线
        parabola2 = axes.plot(lambda x: 0.5*(x-1)**2 + 0.5, color=GREEN, stroke_width=3)
        eq2 = Text("y = 0.5(x-1)² + 0.5", font_size=16, color=GREEN).next_to(eq1, DOWN)
        
        self.play(Transform(parabola1, parabola2), Write(eq2))
        
        # 顶点
        vertex = Dot(axes.coords_to_point(1, 0.5), color=RED, radius=0.08)
        vertex_label = Text("顶点(1,0.5)", font_size=16, color=RED).next_to(vertex, UR)
        
        self.play(Create(vertex), Write(vertex_label))
        self.wait(2)

class Case6InverseFunction(Scene):
    """案例6：反比例函数的双曲线"""
    def construct(self):
        title = Text("反比例函数的双曲线", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 双曲线
        hyperbola_right = axes.plot(lambda x: 1/x, x_range=[0.3, 3], color=BLUE, stroke_width=3)
        hyperbola_left = axes.plot(lambda x: 1/x, x_range=[-3, -0.3], color=BLUE, stroke_width=3)
        
        self.play(Create(hyperbola_right), Create(hyperbola_left))
        
        # 方程
        equation = Text("y = k/x", font_size=24, color=BLUE)
        equation.to_edge(UR)
        self.play(Write(equation))
        
        # 渐近线
        x_asymptote = DashedLine(axes.coords_to_point(-3, 0), axes.coords_to_point(3, 0), color=RED)
        y_asymptote = DashedLine(axes.coords_to_point(0, -3), axes.coords_to_point(0, 3), color=RED)
        
        asymptote_label = Text("渐近线: x=0, y=0", font_size=16, color=RED).to_edge(DR)
        
        self.play(Create(x_asymptote), Create(y_asymptote), Write(asymptote_label))
        self.wait(3)

class Case7TriangleArea(Scene):
    """案例7：三角形面积公式"""
    def construct(self):
        title = Text("三角形面积公式", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 矩形到三角形
        rectangle = Rectangle(width=3, height=2, color=BLUE, stroke_width=3)
        self.play(Create(rectangle))
        
        # 对角线
        diagonal = Line(rectangle.get_corner(DL), rectangle.get_corner(UR), color=RED, stroke_width=3)
        self.play(Create(diagonal))
        
        # 公式
        formula = Text("面积 = (1/2) × 底 × 高", font_size=24, color=GREEN)
        formula.to_edge(DOWN)
        self.play(Write(formula))
        
        self.wait(2)
        
        # 坐标公式
        coord_formula = Text("坐标公式: S = (1/2)|x₁(y₂-y₃) + x₂(y₃-y₁) + x₃(y₁-y₂)|", 
                           font_size=16, color=PURPLE)
        coord_formula.next_to(formula, UP)
        self.play(Write(coord_formula))
        
        self.wait(3)
