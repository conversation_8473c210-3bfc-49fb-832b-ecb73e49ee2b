"""
案例5：二次函数的图像性质
知识要点：抛物线的开口、顶点、对称轴
难度等级：⭐⭐⭐⭐
"""

from manim import *
import numpy as np

class QuadraticFunction(Scene):
    def construct(self):
        # 标题
        title = Text("案例5：二次函数的图像性质", font_size=36, color=BLUE)
        subtitle = Text("从 y=x² 到 y=a(x-h)²+k 的变换过程", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.3)
        
        self.play(Write(title_group))
        self.wait(1)
        self.play(title_group.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[-2, 4, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE, "stroke_width": 2},
            tips=True
        )
        
        x_label = axes.get_x_axis_label("x")
        y_label = axes.get_y_axis_label("y")
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 1. 基础抛物线 y = x²
        basic_text = Text("1. 基础抛物线 y = x²", font_size=24, color=YELLOW)
        basic_text.to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        self.play(Write(basic_text))
        
        # 绘制基础抛物线
        basic_parabola = axes.plot(lambda x: x**2, x_range=[-3, 3], color=BLUE, stroke_width=3)
        basic_equation = MathTex("y = x^2", font_size=24, color=BLUE)
        basic_equation.to_edge(RIGHT, buff=0.5).shift(UP*2)
        
        self.play(Create(basic_parabola), Write(basic_equation))
        
        # 标记顶点
        vertex_dot = Dot(axes.coords_to_point(0, 0), color=RED, radius=0.08)
        vertex_label = Text("顶点(0,0)", font_size=16, color=RED)
        vertex_label.next_to(vertex_dot, DOWN+RIGHT, buff=0.1)
        
        # 对称轴
        symmetry_axis = DashedLine(
            axes.coords_to_point(0, -1),
            axes.coords_to_point(0, 4),
            color=GREEN, stroke_width=2
        )
        axis_label = Text("对称轴: x=0", font_size=16, color=GREEN)
        axis_label.next_to(symmetry_axis, RIGHT, buff=0.1).shift(UP*2)
        
        self.play(Create(vertex_dot), Write(vertex_label))
        self.play(Create(symmetry_axis), Write(axis_label))
        self.wait(2)
        
        # 2. 参数a对开口的影响
        self.play(basic_text.animate.become(
            Text("2. 参数a对开口的影响", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 创建动态参数a
        a_tracker = ValueTracker(1)
        
        dynamic_parabola = always_redraw(lambda:
            axes.plot(lambda x: a_tracker.get_value() * x**2, 
                     x_range=[-3, 3], color=BLUE, stroke_width=3)
        )
        
        dynamic_equation = always_redraw(lambda:
            MathTex(f"y = {a_tracker.get_value():.1f}x^2", 
                   font_size=24, color=BLUE)
            .to_edge(RIGHT, buff=0.5).shift(UP*2)
        )
        
        self.remove(basic_parabola, basic_equation)
        self.add(dynamic_parabola, dynamic_equation)
        
        # 改变a值
        a_values = [2, 0.5, -0.5, -1, 1]
        for a_val in a_values:
            self.play(a_tracker.animate.set_value(a_val), run_time=1.5)
            self.wait(0.5)
        
        # 说明开口方向
        direction_text = Text("a>0: 开口向上; a<0: 开口向下", font_size=18, color=ORANGE)
        direction_text.to_edge(RIGHT, buff=0.5).shift(UP*1)
        self.play(Write(direction_text))
        self.wait(2)
        
        # 3. 水平平移：y = (x-h)²
        self.play(basic_text.animate.become(
            Text("3. 水平平移：y = (x-h)²", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 重置a为1，引入h参数
        a_tracker.set_value(1)
        h_tracker = ValueTracker(0)
        
        dynamic_parabola_h = always_redraw(lambda:
            axes.plot(lambda x: a_tracker.get_value() * (x - h_tracker.get_value())**2, 
                     x_range=[-3, 3], color=GREEN, stroke_width=3)
        )
        
        dynamic_equation_h = always_redraw(lambda:
            MathTex(f"y = (x - {h_tracker.get_value():.1f})^2", 
                   font_size=24, color=GREEN)
            .to_edge(RIGHT, buff=0.5).shift(UP*1.5)
        )
        
        # 动态顶点
        dynamic_vertex = always_redraw(lambda:
            Dot(axes.coords_to_point(h_tracker.get_value(), 0), color=RED, radius=0.08)
        )
        
        # 动态对称轴
        dynamic_axis = always_redraw(lambda:
            DashedLine(
                axes.coords_to_point(h_tracker.get_value(), -1),
                axes.coords_to_point(h_tracker.get_value(), 4),
                color=GREEN, stroke_width=2
            )
        )
        
        self.remove(dynamic_parabola, dynamic_equation, direction_text, 
                   vertex_dot, symmetry_axis)
        self.add(dynamic_parabola_h, dynamic_equation_h, dynamic_vertex, dynamic_axis)
        
        # 改变h值
        h_values = [2, -1, 1, 0]
        for h_val in h_values:
            self.play(h_tracker.animate.set_value(h_val), run_time=2)
            self.wait(0.5)
        
        # 4. 垂直平移：y = (x-h)² + k
        self.play(basic_text.animate.become(
            Text("4. 垂直平移：y = (x-h)² + k", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 引入k参数
        k_tracker = ValueTracker(0)
        
        dynamic_parabola_hk = always_redraw(lambda:
            axes.plot(lambda x: a_tracker.get_value() * (x - h_tracker.get_value())**2 + k_tracker.get_value(), 
                     x_range=[-3, 3], color=PURPLE, stroke_width=3)
        )
        
        dynamic_equation_hk = always_redraw(lambda:
            MathTex(f"y = (x - {h_tracker.get_value():.1f})^2 + {k_tracker.get_value():.1f}", 
                   font_size=20, color=PURPLE)
            .to_edge(RIGHT, buff=0.5).shift(UP*1)
        )
        
        # 动态顶点（包含k）
        dynamic_vertex_hk = always_redraw(lambda:
            Dot(axes.coords_to_point(h_tracker.get_value(), k_tracker.get_value()), 
                color=RED, radius=0.08)
        )
        
        dynamic_vertex_label = always_redraw(lambda:
            Text(f"顶点({h_tracker.get_value():.1f},{k_tracker.get_value():.1f})", 
                 font_size=14, color=RED)
            .next_to(dynamic_vertex_hk, DOWN+RIGHT, buff=0.1)
        )
        
        self.remove(dynamic_parabola_h, dynamic_equation_h, dynamic_vertex, vertex_label)
        self.add(dynamic_parabola_hk, dynamic_equation_hk, dynamic_vertex_hk, dynamic_vertex_label)
        
        # 改变k值
        k_values = [1, -1, 2, 0]
        for k_val in k_values:
            self.play(k_tracker.animate.set_value(k_val), run_time=2)
            self.wait(0.5)
        
        # 5. 综合变换：y = a(x-h)² + k
        self.play(basic_text.animate.become(
            Text("5. 综合变换：y = a(x-h)² + k", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 最终的完整方程
        final_equation = always_redraw(lambda:
            MathTex(f"y = {a_tracker.get_value():.1f}(x - {h_tracker.get_value():.1f})^2 + {k_tracker.get_value():.1f}", 
                   font_size=18, color=PURPLE)
            .to_edge(RIGHT, buff=0.5).shift(UP*0.5)
        )
        
        self.remove(dynamic_equation_hk)
        self.add(final_equation)
        
        # 同时改变多个参数
        self.play(
            a_tracker.animate.set_value(0.5),
            h_tracker.animate.set_value(1),
            k_tracker.animate.set_value(1.5),
            run_time=3
        )
        self.wait(1)
        
        self.play(
            a_tracker.animate.set_value(-0.8),
            h_tracker.animate.set_value(-1),
            k_tracker.animate.set_value(2),
            run_time=3
        )
        self.wait(2)
        
        # 6. 参数意义总结
        self.play(basic_text.animate.become(
            Text("6. 参数意义总结", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 参数说明
        parameter_explanation = VGroup(
            Text("a: 控制开口方向和大小", font_size=16, color=WHITE),
            Text("h: 控制水平平移（顶点x坐标）", font_size=16, color=WHITE),
            Text("k: 控制垂直平移（顶点y坐标）", font_size=16, color=WHITE),
            Text("顶点坐标: (h, k)", font_size=16, color=RED),
            Text("对称轴: x = h", font_size=16, color=GREEN)
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.2)
        parameter_explanation.to_edge(LEFT, buff=0.5).shift(DOWN*1)
        
        self.play(Write(parameter_explanation))
        self.wait(3)
        
        # 总结
        conclusion = Text("二次函数 ↔ 抛物线几何性质", font_size=28, color=GREEN)
        conclusion.to_edge(DOWN, buff=0.2)
        
        self.play(Write(conclusion))
        self.wait(2)
        
        # 淡出
        self.play(*[FadeOut(mob) for mob in self.mobjects])
        self.wait(1)

class QuadraticFunctionDemo(Scene):
    """简化版演示"""
    def construct(self):
        title = Text("二次函数图像性质", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-1, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 基础抛物线
        parabola1 = axes.plot(lambda x: x**2, color=BLUE, stroke_width=3)
        eq1 = MathTex("y = x^2", color=BLUE).to_edge(UR)
        
        self.play(Create(parabola1), Write(eq1))
        self.wait(1)
        
        # 变换抛物线
        parabola2 = axes.plot(lambda x: 0.5*(x-1)**2 + 0.5, color=GREEN, stroke_width=3)
        eq2 = MathTex("y = 0.5(x-1)^2 + 0.5", color=GREEN, font_size=20).next_to(eq1, DOWN)
        
        self.play(Transform(parabola1, parabola2), Write(eq2))
        
        # 顶点
        vertex = Dot(axes.coords_to_point(1, 0.5), color=RED)
        vertex_label = Text("顶点(1,0.5)", font_size=16, color=RED).next_to(vertex, UR)
        
        self.play(Create(vertex), Write(vertex_label))
        self.wait(2)
