#!/usr/bin/env python3
"""
简单的HTTP服务器启动脚本
用于本地测试数学家-WEB前端页面
"""

import http.server
import socketserver
import os
import webbrowser
import threading
import time

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，支持CORS和视频文件"""
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # 为视频文件添加正确的MIME类型
        if self.path.endswith('.mp4'):
            self.send_header('Content-Type', 'video/mp4')
        elif self.path.endswith('.webm'):
            self.send_header('Content-Type', 'video/webm')
        
        super().end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%H:%M:%S')}] {format % args}")

def start_server(port=8000):
    """启动HTTP服务器"""
    
    # 切换到项目根目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print(f"🚀 启动数学家-WEB本地服务器...")
    print(f"📁 服务目录: {os.getcwd()}")
    print(f"🌐 端口: {port}")
    
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"✅ 服务器启动成功！")
            print(f"🔗 访问地址: http://localhost:{port}/frontend/index.html")
            print(f"📺 动画文件: http://localhost:{port}/media/videos/")
            print(f"📚 项目文档: http://localhost:{port}/README.md")
            print(f"\n💡 提示: 按 Ctrl+C 停止服务器")
            
            # 延迟打开浏览器
            def open_browser():
                time.sleep(2)
                webbrowser.open(f'http://localhost:{port}/frontend/index.html')
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Windows: 端口已被占用
            print(f"❌ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_server(port + 1)
        else:
            print(f"❌ 启动服务器失败: {e}")
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")

def main():
    """主函数"""
    print("=" * 60)
    print("🧮 数学家-WEB 本地测试服务器")
    print("=" * 60)
    
    # 检查关键文件
    required_files = [
        'frontend/index.html',
        'media/videos',
        'README.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("⚠️  警告: 以下文件/目录不存在:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print()
    
    # 检查动画文件
    video_dirs = [
        'media/videos/1080p60',
        'media/videos/simple_linear_function/480p15',
        'media/videos/simple_geometry/480p15',
        'media/videos/test_integration/480p15'
    ]
    
    found_videos = []
    for video_dir in video_dirs:
        if os.path.exists(video_dir):
            videos = [f for f in os.listdir(video_dir) if f.endswith('.mp4')]
            for video in videos:
                found_videos.append(f"{video_dir}/{video}")
    
    if found_videos:
        print(f"🎬 发现 {len(found_videos)} 个动画文件:")
        for video in found_videos:
            print(f"   ✅ {video}")
        print()
    else:
        print("⚠️  未发现动画文件，请先运行Manim生成动画")
        print("   示例命令: python -m manim -ql manim-scripts/basic/simple_linear_function.py SimpleLinearFunction")
        print()
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
