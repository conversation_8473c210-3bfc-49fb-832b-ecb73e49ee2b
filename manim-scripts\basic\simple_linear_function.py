"""
简化版一次函数动画演示
不使用LaTeX，适合没有LaTeX环境的系统
"""

from manim import *

class SimpleLinearFunction(Scene):
    def construct(self):
        # 设置标题
        title = Text("一次函数动画演示", font_size=48, color=BLUE)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)

        # 创建简单的坐标系（不使用数字标签）
        axes = Axes(
            x_range=[-5, 5, 1],
            y_range=[-5, 5, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE, "include_numbers": False},
            tips=False,
        )
        
        # 手动添加轴标签
        x_label = Text("x", font_size=24, color=WHITE)
        x_label.next_to(axes.x_axis.get_end(), RIGHT)
        y_label = Text("y", font_size=24, color=WHITE)
        y_label.next_to(axes.y_axis.get_end(), UP)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)

        # 创建函数 y = x
        def linear_func_1(x):
            return x
        
        graph_1 = axes.plot(linear_func_1, color=RED, x_range=[-4, 4])
        equation_1 = Text("y = x", color=RED, font_size=32).to_corner(UL)
        
        self.play(Create(graph_1), Write(equation_1))
        self.wait(2)

        # 变换为 y = 2x
        def linear_func_2(x):
            return 2 * x
        
        graph_2 = axes.plot(linear_func_2, color=GREEN, x_range=[-2, 2])
        equation_2 = Text("y = 2x", color=GREEN, font_size=32).next_to(equation_1, DOWN)
        
        self.play(
            Transform(graph_1, graph_2),
            Write(equation_2)
        )
        self.wait(2)

        # 添加说明文字
        explanation_1 = Text("斜率决定直线的倾斜程度", font_size=24, color=YELLOW)
        explanation_1.to_edge(DOWN)
        self.play(Write(explanation_1))
        self.wait(2)

        # 变换为 y = 2x + 1
        def linear_func_3(x):
            return 2 * x + 1
        
        graph_3 = axes.plot(linear_func_3, color=BLUE, x_range=[-2, 2])
        equation_3 = Text("y = 2x + 1", color=BLUE, font_size=32).next_to(equation_2, DOWN)
        
        self.play(
            Transform(graph_1, graph_3),
            Write(equation_3)
        )
        self.wait(2)

        # 更新说明文字
        explanation_2 = Text("常数项决定y轴截距", font_size=24, color=YELLOW)
        self.play(Transform(explanation_1, explanation_2))
        self.wait(2)

        # 显示截距点
        intercept_point = Dot(axes.coords_to_point(0, 1), color=ORANGE, radius=0.1)
        intercept_label = Text("y截距", font_size=20, color=ORANGE)
        intercept_label.next_to(intercept_point, RIGHT)
        
        self.play(Create(intercept_point), Write(intercept_label))
        self.wait(2)

        # 总结
        summary = Text("一次函数: y = ax + b", font_size=32, color=GOLD)
        summary.to_edge(DOWN)
        self.play(
            FadeOut(explanation_1),
            FadeOut(intercept_point),
            FadeOut(intercept_label),
            Write(summary)
        )
        self.wait(3)

        # 结束动画
        self.play(FadeOut(Group(*self.mobjects)))
        
        end_text = Text("感谢观看！", font_size=48, color=BLUE)
        self.play(Write(end_text))
        self.wait(2)


class InteractiveLinearDemo(Scene):
    """
    交互式演示不同参数的效果
    """
    def construct(self):
        # 创建坐标系
        axes = Axes(
            x_range=[-5, 5, 1],
            y_range=[-5, 5, 1],
            x_length=10,
            y_length=8,
            axis_config={"include_numbers": False}
        )
        
        self.add(axes)
        
        # 参数控制器
        a_value = ValueTracker(1)
        b_value = ValueTracker(0)
        
        # 动态函数图像
        def get_line():
            return axes.plot(
                lambda x: a_value.get_value() * x + b_value.get_value(),
                color=BLUE,
                x_range=[-4, 4]
            )
        
        line = always_redraw(get_line)
        
        # 动态方程显示
        def get_equation():
            a_val = a_value.get_value()
            b_val = b_value.get_value()
            if b_val >= 0:
                eq_text = f"y = {a_val:.1f}x + {b_val:.1f}"
            else:
                eq_text = f"y = {a_val:.1f}x - {abs(b_val):.1f}"
            return Text(eq_text, font_size=32).to_corner(UL)
        
        equation = always_redraw(get_equation)
        
        self.add(line, equation)
        
        # 演示不同的参数组合
        parameter_sets = [
            (2, 1),    # y = 2x + 1
            (-1, 2),   # y = -x + 2
            (0.5, -1), # y = 0.5x - 1
            (3, 0),    # y = 3x
            (1, 0),    # 回到 y = x
        ]
        
        for a, b in parameter_sets:
            self.play(
                a_value.animate.set_value(a),
                b_value.animate.set_value(b),
                run_time=2
            )
            self.wait(1)
        
        self.wait(2)
