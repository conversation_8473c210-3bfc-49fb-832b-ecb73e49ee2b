package com.mathweb.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 数学案例实体类
 * 
 * <AUTHOR> Animator Team
 */
@Entity
@Table(name = "math_cases", indexes = {
    @Index(name = "idx_case_level", columnList = "level"),
    @Index(name = "idx_case_category", columnList = "category"),
    @Index(name = "idx_case_created_at", columnList = "created_at")
})
public class MathCase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "案例标题不能为空")
    @Size(max = 200, message = "案例标题长度不能超过200个字符")
    @Column(nullable = false, length = 200)
    private String title;

    @Size(max = 1000, message = "案例描述长度不能超过1000个字符")
    @Column(length = 1000)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private CaseLevel level = CaseLevel.BASIC;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 30)
    private CaseCategory category;

    @NotBlank(message = "脚本路径不能为空")
    @Column(name = "script_path", nullable = false, length = 500)
    private String scriptPath;

    @Column(name = "theory_path", length = 500)
    private String theoryPath;

    @Column(name = "thumbnail_url", length = 500)
    private String thumbnailUrl;

    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    @Column(name = "view_count", nullable = false)
    private Long viewCount = 0L;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", referencedColumnName = "id")
    private User createdBy;

    // 案例难度级别枚举
    public enum CaseLevel {
        BASIC("基础"),
        INTERMEDIATE("中级"),
        ADVANCED("高级");

        private final String displayName;

        CaseLevel(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 案例分类枚举
    public enum CaseCategory {
        GEOMETRY("几何"),
        ALGEBRA("代数"),
        FUNCTION("函数"),
        TRIGONOMETRY("三角函数"),
        CALCULUS("微积分"),
        STATISTICS("统计"),
        PROBABILITY("概率"),
        NUMBER_THEORY("数论");

        private final String displayName;

        CaseCategory(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 构造函数
    public MathCase() {
    }

    public MathCase(String title, String description, CaseLevel level, CaseCategory category, String scriptPath) {
        this.title = title;
        this.description = description;
        this.level = level;
        this.category = category;
        this.scriptPath = scriptPath;
    }

    // JPA生命周期回调
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // 业务方法
    public void incrementViewCount() {
        this.viewCount++;
    }

    public boolean isAccessibleByLevel(User.UserRole userRole) {
        return switch (userRole) {
            case STUDENT -> level == CaseLevel.BASIC;
            case TEACHER -> level == CaseLevel.BASIC || level == CaseLevel.INTERMEDIATE;
            case ADMIN -> true;
        };
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public CaseLevel getLevel() {
        return level;
    }

    public void setLevel(CaseLevel level) {
        this.level = level;
    }

    public CaseCategory getCategory() {
        return category;
    }

    public void setCategory(CaseCategory category) {
        this.category = category;
    }

    public String getScriptPath() {
        return scriptPath;
    }

    public void setScriptPath(String scriptPath) {
        this.scriptPath = scriptPath;
    }

    public String getTheoryPath() {
        return theoryPath;
    }

    public void setTheoryPath(String theoryPath) {
        this.theoryPath = theoryPath;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public Integer getDurationSeconds() {
        return durationSeconds;
    }

    public void setDurationSeconds(Integer durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    public Long getViewCount() {
        return viewCount;
    }

    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public User getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(User createdBy) {
        this.createdBy = createdBy;
    }

    // equals和hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MathCase mathCase = (MathCase) o;
        return Objects.equals(id, mathCase.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "MathCase{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", level=" + level +
                ", category=" + category +
                ", scriptPath='" + scriptPath + '\'' +
                ", viewCount=" + viewCount +
                ", isActive=" + isActive +
                ", createdAt=" + createdAt +
                '}';
    }
}
