import React, { useState } from 'react'
import {
  Container,
  <PERSON><PERSON><PERSON>,
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Link,
  Alert,
  Divider,
} from '@mui/material'
import { useNavigate } from 'react-router-dom'

const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    try {
      // 模拟登录请求
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 简单验证
      if (formData.email === '<EMAIL>' && formData.password === 'demo123') {
        // 登录成功
        localStorage.setItem('user', JSON.stringify({
          id: 1,
          email: formData.email,
          name: '演示用户',
          role: 'student',
        }))
        navigate('/')
      } else {
        setError('邮箱或密码错误')
      }
    } catch (err) {
      setError('登录失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
      }}
    >
      <Container maxWidth="sm">
        <Card sx={{ borderRadius: 3, boxShadow: '0 20px 40px rgba(0,0,0,0.1)' }}>
          <CardContent sx={{ p: 4 }}>
            {/* 标题 */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h3" component="h1" sx={{ mb: 1, fontWeight: 700, color: '#667eea' }}>
                🧮
              </Typography>
              <Typography variant="h4" component="h1" sx={{ mb: 1, fontWeight: 600 }}>
                欢迎回来
              </Typography>
              <Typography variant="body1" color="text.secondary">
                登录到数学家-WEB学习平台
              </Typography>
            </Box>

            {/* 错误提示 */}
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {/* 演示账号提示 */}
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                <strong>演示账号：</strong><br />
                邮箱：<EMAIL><br />
                密码：demo123
              </Typography>
            </Alert>

            {/* 登录表单 */}
            <Box component="form" onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="邮箱地址"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                sx={{ mb: 3 }}
                autoComplete="email"
              />
              <TextField
                fullWidth
                label="密码"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                required
                sx={{ mb: 3 }}
                autoComplete="current-password"
              />
              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{
                  mb: 3,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 2,
                }}
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Box>

            {/* 分割线 */}
            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                或
              </Typography>
            </Divider>

            {/* 其他选项 */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                还没有账号？{' '}
                <Link href="#" underline="hover" sx={{ fontWeight: 600 }}>
                  立即注册
                </Link>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <Link href="#" underline="hover">
                  忘记密码？
                </Link>
              </Typography>
            </Box>

            {/* 返回首页 */}
            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Button
                variant="text"
                onClick={() => navigate('/')}
                sx={{ color: 'text.secondary' }}
              >
                返回首页
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* 页面底部信息 */}
        <Box sx={{ textAlign: 'center', mt: 4, color: 'rgba(255,255,255,0.8)' }}>
          <Typography variant="body2">
            数学家-WEB - 让数学学习更生动有趣
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.7 }}>
            技术栈：React + Spring Boot + PostgreSQL + Manim
          </Typography>
        </Box>
      </Container>
    </Box>
  )
}

export default LoginPage
