package com.mathweb.repository;

import com.mathweb.entity.MathCase;
import com.mathweb.entity.User;
import com.mathweb.entity.UserActivity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户活动数据访问接口
 * 
 * <AUTHOR> Animator Team
 */
@Repository
public interface UserActivityRepository extends JpaRepository<UserActivity, Long> {

    /**
     * 根据用户和案例查找活动记录
     */
    Optional<UserActivity> findByUserAndMathCase(User user, MathCase mathCase);

    /**
     * 根据用户ID和案例ID查找活动记录
     */
    @Query("SELECT ua FROM UserActivity ua WHERE ua.user.id = :userId AND ua.mathCase.id = :caseId")
    Optional<UserActivity> findByUserIdAndCaseId(@Param("userId") Long userId, @Param("caseId") Long caseId);

    /**
     * 查找用户的所有活动记录
     */
    List<UserActivity> findByUserOrderByLastAccessedDesc(User user);

    /**
     * 分页查找用户的活动记录
     */
    Page<UserActivity> findByUser(User user, Pageable pageable);

    /**
     * 查找用户最近访问的案例
     */
    Page<UserActivity> findByUserOrderByLastAccessedDesc(User user, Pageable pageable);

    /**
     * 查找用户收藏的案例
     */
    List<UserActivity> findByUserAndIsFavoriteTrueOrderByLastAccessedDesc(User user);

    /**
     * 分页查找用户收藏的案例
     */
    Page<UserActivity> findByUserAndIsFavoriteTrue(User user, Pageable pageable);

    /**
     * 查找用户已完成的案例
     */
    List<UserActivity> findByUserAndIsCompletedTrueOrderByLastAccessedDesc(User user);

    /**
     * 分页查找用户已完成的案例
     */
    Page<UserActivity> findByUserAndIsCompletedTrue(User user, Pageable pageable);

    /**
     * 查找案例的所有活动记录
     */
    List<UserActivity> findByMathCaseOrderByLastAccessedDesc(MathCase mathCase);

    /**
     * 分页查找案例的活动记录
     */
    Page<UserActivity> findByMathCase(MathCase mathCase, Pageable pageable);

    /**
     * 查找指定时间之后的用户活动
     */
    List<UserActivity> findByLastAccessedAfter(LocalDateTime dateTime);

    /**
     * 查找用户在指定时间段内的活动
     */
    List<UserActivity> findByUserAndLastAccessedBetween(User user, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计用户的总学习时间
     */
    @Query("SELECT SUM(ua.totalWatchTimeSeconds) FROM UserActivity ua WHERE ua.user = :user")
    Long getTotalWatchTimeByUser(@Param("user") User user);

    /**
     * 统计用户访问的案例数量
     */
    @Query("SELECT COUNT(ua) FROM UserActivity ua WHERE ua.user = :user")
    Long getAccessedCaseCountByUser(@Param("user") User user);

    /**
     * 统计用户完成的案例数量
     */
    @Query("SELECT COUNT(ua) FROM UserActivity ua WHERE ua.user = :user AND ua.isCompleted = true")
    Long getCompletedCaseCountByUser(@Param("user") User user);

    /**
     * 统计用户收藏的案例数量
     */
    @Query("SELECT COUNT(ua) FROM UserActivity ua WHERE ua.user = :user AND ua.isFavorite = true")
    Long getFavoriteCaseCountByUser(@Param("user") User user);

    /**
     * 统计案例的总观看时间
     */
    @Query("SELECT SUM(ua.totalWatchTimeSeconds) FROM UserActivity ua WHERE ua.mathCase = :mathCase")
    Long getTotalWatchTimeByCase(@Param("mathCase") MathCase mathCase);

    /**
     * 统计案例的访问用户数量
     */
    @Query("SELECT COUNT(DISTINCT ua.user) FROM UserActivity ua WHERE ua.mathCase = :mathCase")
    Long getUniqueUserCountByCase(@Param("mathCase") MathCase mathCase);

    /**
     * 统计案例的完成用户数量
     */
    @Query("SELECT COUNT(ua) FROM UserActivity ua WHERE ua.mathCase = :mathCase AND ua.isCompleted = true")
    Long getCompletedUserCountByCase(@Param("mathCase") MathCase mathCase);

    /**
     * 查找最活跃的用户
     */
    @Query("SELECT ua.user, SUM(ua.totalWatchTimeSeconds) as totalTime FROM UserActivity ua " +
           "GROUP BY ua.user ORDER BY totalTime DESC")
    Page<Object[]> findMostActiveUsers(Pageable pageable);

    /**
     * 查找最受欢迎的案例
     */
    @Query("SELECT ua.mathCase, COUNT(ua) as accessCount FROM UserActivity ua " +
           "GROUP BY ua.mathCase ORDER BY accessCount DESC")
    Page<Object[]> findMostPopularCases(Pageable pageable);

    /**
     * 查找用户的学习进度统计
     */
    @Query("SELECT " +
           "COUNT(ua) as totalCases, " +
           "SUM(CASE WHEN ua.isCompleted = true THEN 1 ELSE 0 END) as completedCases, " +
           "SUM(CASE WHEN ua.isFavorite = true THEN 1 ELSE 0 END) as favoriteCases, " +
           "SUM(ua.totalWatchTimeSeconds) as totalWatchTime " +
           "FROM UserActivity ua WHERE ua.user = :user")
    Object[] getUserLearningStats(@Param("user") User user);

    /**
     * 查找案例的统计信息
     */
    @Query("SELECT " +
           "COUNT(DISTINCT ua.user) as uniqueUsers, " +
           "SUM(ua.accessCount) as totalAccess, " +
           "SUM(ua.totalWatchTimeSeconds) as totalWatchTime, " +
           "AVG(ua.completionPercentage) as avgCompletion " +
           "FROM UserActivity ua WHERE ua.mathCase = :mathCase")
    Object[] getCaseStats(@Param("mathCase") MathCase mathCase);

    /**
     * 查找用户最近学习的案例（排除已完成的）
     */
    @Query("SELECT ua FROM UserActivity ua WHERE ua.user = :user AND ua.isCompleted = false " +
           "ORDER BY ua.lastAccessed DESC")
    Page<UserActivity> findRecentIncompleteCases(@Param("user") User user, Pageable pageable);

    /**
     * 查找用户推荐的案例（基于学习历史）
     */
    @Query("SELECT mc FROM MathCase mc WHERE mc.isActive = true AND mc.id NOT IN " +
           "(SELECT ua.mathCase.id FROM UserActivity ua WHERE ua.user = :user) " +
           "AND mc.category IN " +
           "(SELECT DISTINCT ua.mathCase.category FROM UserActivity ua WHERE ua.user = :user) " +
           "ORDER BY mc.viewCount DESC")
    Page<MathCase> findRecommendedCasesForUser(@Param("user") User user, Pageable pageable);

    /**
     * 检查用户是否已访问过案例
     */
    boolean existsByUserAndMathCase(User user, MathCase mathCase);
}
