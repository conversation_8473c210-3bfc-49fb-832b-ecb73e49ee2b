version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: mathweb-postgres
    environment:
      POSTGRES_DB: mathweb
      POSTGRES_USER: mathweb_user
      POSTGRES_PASSWORD: mathweb_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - mathweb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mathweb_user -d mathweb"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: mathweb-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mathweb-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端Spring Boot应用
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: mathweb-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DATABASE_URL: ***************************************
      DATABASE_USERNAME: mathweb_user
      DATABASE_PASSWORD: mathweb_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "8080:8080"
    volumes:
      - ../manim-scripts:/app/manim-scripts:ro
      - animation_cache:/app/animations
      - upload_files:/app/uploads
    networks:
      - mathweb-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端React应用
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
    container_name: mathweb-frontend
    ports:
      - "3000:80"
    networks:
      - mathweb-network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: mathweb-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - animation_cache:/var/www/animations:ro
    networks:
      - mathweb-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  animation_cache:
    driver: local
  upload_files:
    driver: local

networks:
  mathweb-network:
    driver: bridge
