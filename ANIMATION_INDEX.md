# 数学动画案例索引

## 📚 案例列表

### 第一部分：基础几何与代数对应

#### 坐标系中的点 ⭐⭐
- **描述**: 点的坐标表示与距离公式
- **脚本**: `part1_basic_geometry/case1_coordinate_points.py`
- **类名**: `CoordinatePointsDemo`
- **视频路径**: `media/videos/cases/coordinatepointsdemo/480p15/CoordinatePointsDemo.mp4`

#### 直线的方程 ⭐⭐⭐
- **描述**: 点斜式、截距式、一般式的相互转换
- **脚本**: `part1_basic_geometry/case2_line_equations.py`
- **类名**: `LineEquationsDemo`
- **视频路径**: `media/videos/cases/lineequationsdemo/480p15/LineEquationsDemo.mp4`

#### 圆的方程 ⭐⭐⭐
- **描述**: 圆的形成过程与方程推导
- **脚本**: `part1_basic_geometry/case3_circle_equations.py`
- **类名**: `CircleEquationsDemo`
- **视频路径**: `media/videos/cases/circleequationsdemo/480p15/CircleEquationsDemo.mp4`

### 第二部分：函数图像与几何变换

#### 一次函数图像变换 ⭐⭐
- **描述**: 从 y=kx 到 y=kx+b 的图像变化
- **脚本**: `part2_functions/case4_linear_function_transform.py`
- **类名**: `LinearFunctionTransformDemo`
- **视频路径**: `media/videos/cases/linearfunctiontransformdemo/480p15/LinearFunctionTransformDemo.mp4`

#### 二次函数的图像性质 ⭐⭐⭐⭐
- **描述**: 抛物线的开口、顶点、对称轴
- **脚本**: `part2_functions/case5_quadratic_function.py`
- **类名**: `QuadraticFunctionDemo`
- **视频路径**: `media/videos/cases/quadraticfunctiondemo/480p15/QuadraticFunctionDemo.mp4`

#### 反比例函数的双曲线 ⭐⭐⭐
- **描述**: y = k/x 的图像特征与渐近线
- **脚本**: `part2_functions/case6_inverse_function.py`
- **类名**: `InverseFunctionDemo`
- **视频路径**: `media/videos/cases/inversefunctiondemo/480p15/InverseFunctionDemo.mp4`

### 第三部分：三角形的几何与代数

#### 三角形面积公式的推导 ⭐⭐⭐
- **描述**: 从矩形面积到三角形面积
- **脚本**: `part3_triangles/case7_triangle_area.py`
- **类名**: `TriangleAreaDemo`
- **视频路径**: `media/videos/cases/triangleareademo/480p15/TriangleAreaDemo.mp4`


## 🎬 生成说明

使用以下命令生成单个动画：
```bash
python -m manim -ql manim-scripts/cases/[script_path] [ClassName]
```

使用批量生成脚本：
```bash
python generate_all_animations.py
```

## 📊 质量设置

- `-ql`: 低质量 (480p15)
- `-qm`: 中等质量 (720p30)
- `-qh`: 高质量 (1080p60)
- `-qk`: 4K质量 (2160p60)
