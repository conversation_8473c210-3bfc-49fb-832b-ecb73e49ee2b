# 数学动画WEB网站精简架构方案

目标：完成一个面向中学用户的数学动画WEB网站，用户可以选择不同的几何代数转换案例，网站调用内置的manim引擎生成动画展示几何代数的转换。

## 一、系统架构
1. **分层设计**：
   - 展示层：React前端应用
   - 业务层：Spring Boot REST API
   - 数据层：PostgreSQL数据库
   - 渲染层：Python/Manim本地进程

2. **通信流程**：
   ```mermaid
   graph LR
   A[浏览器] -->|HTTP/JSON| B(Spring Boot)
   B -->|JDBC| C[(PostgreSQL)]
   B -->|子进程调用| D[Manim引擎]
   D -->|文件IO| E[本地存储]
   ```

## 二、技术组件
| 层级        | 技术选型                 | 核心功能                     |
|-------------|--------------------------|-----------------------------|
| **前端**    | React 18 + TypeScript     | 案例展示/动画播放/用户交互   |
| **状态管理**| Redux Toolkit            | 应用状态/用户会话管理        |
| **后端**    | Spring Boot 3.x          | REST API/业务逻辑处理        |
| **安全**    | Spring Security + JWT    | 认证/授权/访问控制           |
| **数据库**  | PostgreSQL 13+           | 结构化数据存储               |
| **动画**    | Manim Community Edition  | 数学动画生成                 |

## 三、核心模块
### 1. 用户管理
- **角色划分**：
  - 管理员：系统配置+内容管理
  - 高级用户：访问全部案例
  - 普通用户：基础案例权限

- **关键流程**：
  - 注册→邮箱验证→登录→JWT签发
  - 每次请求携带JWT进行鉴权

### 2. 案例管理
- **目录结构**：
  ```
  /resources
    /cases
      /basic
        case1.py
        case1.md
      /advanced
    /animations
  ```

- **元数据字段**：
  - 案例ID
  - 标题与描述
  - 难度等级（basic/advanced）
  - 脚本路径
  - 理论文档路径

### 3. 动画服务
- **生成流程**：
  1. 接收案例ID和参数
  2. 校验用户权限
  3. 检查动画缓存
  4. 调用Manim生成：
     ```bash
     manim -ql {script.py} -o {output_dir}
     ```
  5. 返回视频文件路径

- **缓存策略**：
  - 基于案例ID+参数哈希缓存
  - 本地文件系统存储7天

## 四、数据设计
### 1. 主要表结构
**users**
- id (PK)
- username (UNIQUE)
- email (UNIQUE)
- password_hash
- role (ENUM)
- created_at

**math_cases**
- id (PK)
- title
- description
- level (ENUM)
- script_path
- theory_path
- created_at

**user_activities**
- user_id (FK)
- case_id (FK)
- last_accessed
- access_count

### 2. 索引规划
- 用户表：email唯一索引
- 案例表：level普通索引
- 活动表：(user_id, case_id)复合索引

## 五、接口规范
### 1. 认证接口
- `POST /auth/register` 用户注册
- `POST /auth/login` 用户登录
- `GET /auth/profile` 获取当前用户信息

### 2. 案例接口
- `GET /cases` 获取可见案例列表
- `GET /cases/{id}` 获取案例详情
- `GET /cases/{id}/animate` 生成动画

### 3. 管理接口（需admin角色）
- `POST /admin/cases` 新增案例
- `PUT /admin/cases/{id}` 更新案例

## 六、安全方案
1. **传输安全**：
   - 强制HTTPS（开发环境可用HTTP）
   - 敏感字段加密传输

2. **访问控制**：
   - 角色注解校验（@PreAuthorize）
   - 案例级别权限过滤

3. **输入验证**：
   - 所有API参数白名单校验
   - 文件路径防目录遍历

4. **输出防护**：
   - 响应头XSS防护
   - JSON数据转义处理

## 七、本地运行配置
1. **环境要求**：
   - Java 17+
   - Node.js 16+
   - Python 3.8+
   - PostgreSQL 13+
   - ManimCE（pip安装）

2. **目录权限**：
   - /cases：只读（755）
   - /animations：读写（775）

3. **启动顺序**：
   1. 启动PostgreSQL服务
   2. 运行Spring Boot应用
   3. 启动React开发服务器

## 八、扩展设计
1. **案例扩展**：
   - 按规范添加脚本到/cases目录
   - 通过管理接口注册元数据

2. **功能扩展点**：
   - 案例收藏功能（user_activities扩展）
   - 动画参数自定义（扩展animate接口）
   - 用户学习进度跟踪

