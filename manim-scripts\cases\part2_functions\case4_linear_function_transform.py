"""
案例4：一次函数图像变换
知识要点：一次函数的图像特征、平移变换
难度等级：⭐⭐
"""

from manim import *
import numpy as np

class LinearFunctionTransform(Scene):
    def construct(self):
        # 标题
        title = Text("案例4：一次函数图像变换", font_size=36, color=BLUE)
        subtitle = Text("从 y=kx 到 y=kx+b 的图像变化", font_size=24, color=GRAY)
        title_group = VGroup(title, subtitle).arrange(DOWN, buff=0.3)
        
        self.play(Write(title_group))
        self.wait(1)
        self.play(title_group.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[-3, 3, 1],
            x_length=8,
            y_length=6,
            axis_config={"color": WHITE, "stroke_width": 2},
            tips=True
        )
        
        x_label = axes.get_x_axis_label("x")
        y_label = axes.get_y_axis_label("y")
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        self.wait(1)
        
        # 1. 基础函数 y = kx
        basic_text = Text("1. 基础函数 y = kx", font_size=24, color=YELLOW)
        basic_text.to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        self.play(Write(basic_text))
        
        # 绘制 y = x
        k1 = 1
        line1 = axes.plot(lambda x: k1 * x, x_range=[-3, 3], color=BLUE, stroke_width=3)
        equation1 = MathTex(f"y = {k1}x", font_size=24, color=BLUE)
        equation1.to_edge(RIGHT, buff=0.5).shift(UP*2)
        
        self.play(Create(line1), Write(equation1))
        self.wait(1)
        
        # 2. 斜率k对图像倾斜程度的影响
        self.play(basic_text.animate.become(
            Text("2. 斜率k的影响", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 创建动态斜率
        k_tracker = ValueTracker(1)
        
        dynamic_line = always_redraw(lambda:
            axes.plot(lambda x: k_tracker.get_value() * x, 
                     x_range=[-3, 3], color=BLUE, stroke_width=3)
        )
        
        dynamic_equation = always_redraw(lambda:
            MathTex(f"y = {k_tracker.get_value():.1f}x", 
                   font_size=24, color=BLUE)
            .to_edge(RIGHT, buff=0.5).shift(UP*2)
        )
        
        self.remove(line1, equation1)
        self.add(dynamic_line, dynamic_equation)
        
        # 改变斜率值
        slope_values = [2, 0.5, -1, -2, 1]
        for k_val in slope_values:
            self.play(k_tracker.animate.set_value(k_val), run_time=1.5)
            self.wait(0.5)
        
        # 3. 截距b对图像位置的影响
        self.play(basic_text.animate.become(
            Text("3. 截距b的影响：y = kx + b", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 固定斜率为1，改变截距
        k_tracker.set_value(1)
        b_tracker = ValueTracker(0)
        
        dynamic_line_with_b = always_redraw(lambda:
            axes.plot(lambda x: k_tracker.get_value() * x + b_tracker.get_value(), 
                     x_range=[-3, 3], color=GREEN, stroke_width=3)
        )
        
        dynamic_equation_with_b = always_redraw(lambda:
            MathTex(f"y = {k_tracker.get_value():.1f}x + {b_tracker.get_value():.1f}", 
                   font_size=24, color=GREEN)
            .to_edge(RIGHT, buff=0.5).shift(UP*1.5)
        )
        
        # 保留原来的线作为参考
        reference_line = axes.plot(lambda x: x, x_range=[-3, 3], 
                                 color=BLUE, stroke_width=2, stroke_opacity=0.5)
        reference_equation = MathTex("y = x", font_size=20, color=BLUE, fill_opacity=0.5)
        reference_equation.to_edge(RIGHT, buff=0.5).shift(UP*2.5)
        
        self.remove(dynamic_line, dynamic_equation)
        self.add(reference_line, reference_equation, dynamic_line_with_b, dynamic_equation_with_b)
        
        # 改变截距值
        b_values = [1, 2, -1, -2, 0]
        for b_val in b_values:
            self.play(b_tracker.animate.set_value(b_val), run_time=1.5)
            self.wait(0.5)
        
        # 4. y截距的几何意义
        self.play(basic_text.animate.become(
            Text("4. y截距的几何意义", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 设置一个具体的截距值
        b_tracker.set_value(1.5)
        
        # 标记y截距点
        y_intercept_point = Dot(axes.coords_to_point(0, b_tracker.get_value()), 
                               color=RED, radius=0.08)
        y_intercept_label = Text(f"y截距 = {b_tracker.get_value()}", 
                               font_size=16, color=RED)
        y_intercept_label.next_to(y_intercept_point, LEFT, buff=0.2)
        
        # 垂直虚线
        vertical_line = DashedLine(
            axes.coords_to_point(0, 0),
            axes.coords_to_point(0, b_tracker.get_value()),
            color=RED, stroke_width=2
        )
        
        self.play(Create(y_intercept_point), Write(y_intercept_label), Create(vertical_line))
        self.wait(2)
        
        # 5. 综合演示：同时改变k和b
        self.play(basic_text.animate.become(
            Text("5. 综合演示：k和b同时变化", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 清除一些元素
        self.play(FadeOut(y_intercept_point), FadeOut(y_intercept_label), 
                 FadeOut(vertical_line), FadeOut(reference_line), FadeOut(reference_equation))
        
        # 创建多条直线展示不同的k和b组合
        combinations = [(1, 0), (2, 1), (0.5, -1), (-1, 2), (-0.5, -0.5)]
        colors = [BLUE, GREEN, YELLOW, RED, PURPLE]
        
        lines = []
        equations = []
        
        for i, (k, b) in enumerate(combinations):
            line = axes.plot(lambda x, k=k, b=b: k * x + b, 
                           x_range=[-3, 3], color=colors[i], stroke_width=2)
            equation = MathTex(f"y = {k}x + {b}", font_size=16, color=colors[i])
            equation.to_edge(RIGHT, buff=0.2).shift(DOWN * (i * 0.4 - 1))
            
            lines.append(line)
            equations.append(equation)
        
        # 逐个显示
        for line, equation in zip(lines, equations):
            self.play(Create(line), Write(equation), run_time=0.8)
            self.wait(0.3)
        
        self.wait(2)
        
        # 6. 平移变换的本质
        self.play(basic_text.animate.become(
            Text("6. 平移变换的本质", font_size=24, color=YELLOW)
            .to_edge(LEFT, buff=0.5).to_edge(UP, buff=1.5)
        ))
        
        # 清除多条线，只保留一条进行平移演示
        self.play(*[FadeOut(line) for line in lines[1:]], 
                 *[FadeOut(eq) for eq in equations[1:]])
        
        # 重新设置为简单情况
        k_tracker.set_value(1)
        b_tracker.set_value(0)
        
        # 显示平移过程
        transform_text = Text("y = x → y = x + b (垂直平移)", font_size=20, color=ORANGE)
        transform_text.to_edge(DOWN, buff=1)
        self.play(Write(transform_text))
        
        # 平移动画
        self.play(b_tracker.animate.set_value(2), run_time=3)
        self.wait(1)
        self.play(b_tracker.animate.set_value(-1), run_time=3)
        self.wait(1)
        
        # 总结
        conclusion = Text("函数解析式 ↔ 几何图像变换", font_size=28, color=GREEN)
        conclusion.to_edge(DOWN, buff=0.2)
        
        self.play(Write(conclusion))
        self.wait(2)
        
        # 淡出
        self.play(*[FadeOut(mob) for mob in self.mobjects])
        self.wait(1)

class LinearFunctionTransformDemo(Scene):
    """简化版演示"""
    def construct(self):
        title = Text("一次函数图像变换", font_size=32, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        self.play(title.animate.to_edge(UP))
        
        # 坐标系
        axes = Axes(x_range=[-3, 3], y_range=[-3, 3], x_length=6, y_length=6)
        self.play(Create(axes))
        
        # 基础直线 y = x
        line1 = axes.plot(lambda x: x, color=BLUE, stroke_width=3)
        eq1 = MathTex("y = x", color=BLUE).to_edge(UR)
        
        self.play(Create(line1), Write(eq1))
        self.wait(1)
        
        # 变换为 y = x + 1
        line2 = axes.plot(lambda x: x + 1, color=GREEN, stroke_width=3)
        eq2 = MathTex("y = x + 1", color=GREEN).next_to(eq1, DOWN)
        
        self.play(Transform(line1, line2), Write(eq2))
        self.wait(1)
        
        # 变换为 y = 2x + 1
        line3 = axes.plot(lambda x: 2*x + 1, color=RED, stroke_width=3)
        eq3 = MathTex("y = 2x + 1", color=RED).next_to(eq2, DOWN)
        
        self.play(Transform(line1, line3), Write(eq3))
        self.wait(2)
