import React from 'react'
import {
  Container,
  Typography,
  Box,
  AppBar,
  Toolbar,
  Button,
  Card,
  CardContent,
  Chip,
  Paper,
  Grid,
} from '@mui/material'
import { ArrowBack, PlayArrow, Favorite, Share } from '@mui/icons-material'
import { useNavigate, useParams } from 'react-router-dom'

const CaseDetailPage: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()

  // 模拟案例详情数据
  const caseDetail = {
    id: parseInt(id || '1'),
    title: '一次函数动画演示',
    description: '通过动画演示一次函数 y = ax + b 的图像变化，深入理解参数 a 和 b 对函数图像的影响。',
    level: 'basic',
    category: '代数',
    icon: '📈',
    duration: '3分钟',
    viewCount: 1250,
    createdAt: '2024-12-01',
    author: '数学老师',
    videoUrl: '/media/videos/simple_linear_function/480p15/SimpleLinearFunction.mp4',
    theory: `
## 一次函数基础知识

一次函数是形如 y = ax + b (a ≠ 0) 的函数，其中：
- a 称为斜率，决定直线的倾斜程度
- b 称为 y 轴截距，决定直线与 y 轴的交点

### 参数影响
1. **斜率 a 的影响**：
   - a > 0 时，函数递增
   - a < 0 时，函数递减
   - |a| 越大，直线越陡峭

2. **截距 b 的影响**：
   - b > 0 时，直线向上平移
   - b < 0 时，直线向下平移
   - b = 0 时，直线过原点

### 实际应用
一次函数在生活中有广泛应用，如：
- 匀速运动的路程-时间关系
- 商品价格与数量的关系
- 温度的华氏度与摄氏度转换
    `,
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'basic':
        return '#4caf50'
      case 'intermediate':
        return '#ff9800'
      case 'advanced':
        return '#f44336'
      default:
        return '#9e9e9e'
    }
  }

  const getLevelText = (level: string) => {
    switch (level) {
      case 'basic':
        return '基础'
      case 'intermediate':
        return '中级'
      case 'advanced':
        return '高级'
      default:
        return '未知'
    }
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      {/* 导航栏 */}
      <AppBar position="static">
        <Toolbar>
          <Button
            color="inherit"
            startIcon={<ArrowBack />}
            onClick={() => navigate('/cases')}
            sx={{ mr: 2 }}
          >
            返回案例库
          </Button>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            🧮 数学家-WEB
          </Typography>
          <Button color="inherit" onClick={() => navigate('/')}>
            首页
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Grid container spacing={4}>
          {/* 左侧：视频播放区域 */}
          <Grid item xs={12} md={8}>
            <Card sx={{ mb: 3 }}>
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: 400,
                  bgcolor: '#000',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '4px 4px 0 0',
                }}
              >
                {/* 视频播放器占位 */}
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    color: 'white',
                  }}
                >
                  <PlayArrow sx={{ fontSize: 80, mb: 2 }} />
                  <Typography variant="h6">点击播放动画</Typography>
                  <Typography variant="body2" sx={{ mt: 1, opacity: 0.7 }}>
                    动画文件：{caseDetail.videoUrl}
                  </Typography>
                </Box>
              </Box>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 600, flex: 1 }}>
                    {caseDetail.title}
                  </Typography>
                  <Chip
                    label={getLevelText(caseDetail.level)}
                    sx={{
                      bgcolor: getLevelColor(caseDetail.level),
                      color: 'white',
                      fontWeight: 500,
                      ml: 2,
                    }}
                  />
                </Box>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                  {caseDetail.description}
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                  <Button variant="contained" startIcon={<PlayArrow />} size="large">
                    播放动画
                  </Button>
                  <Button variant="outlined" startIcon={<Favorite />}>
                    收藏
                  </Button>
                  <Button variant="outlined" startIcon={<Share />}>
                    分享
                  </Button>
                </Box>
                <Box sx={{ display: 'flex', gap: 3, color: 'text.secondary', fontSize: '0.875rem' }}>
                  <span>分类：{caseDetail.category}</span>
                  <span>时长：{caseDetail.duration}</span>
                  <span>观看：{caseDetail.viewCount} 次</span>
                  <span>作者：{caseDetail.author}</span>
                </Box>
              </CardContent>
            </Card>

            {/* 理论知识 */}
            <Card>
              <CardContent>
                <Typography variant="h5" component="h2" sx={{ mb: 3, fontWeight: 600 }}>
                  理论知识
                </Typography>
                <Paper sx={{ p: 3, bgcolor: '#f8f9fa' }}>
                  <Typography
                    component="div"
                    sx={{
                      '& h2': { fontSize: '1.5rem', fontWeight: 600, mb: 2, mt: 3 },
                      '& h3': { fontSize: '1.25rem', fontWeight: 600, mb: 1.5, mt: 2 },
                      '& p': { mb: 2, lineHeight: 1.6 },
                      '& ul': { mb: 2, pl: 3 },
                      '& li': { mb: 0.5 },
                    }}
                  >
                    {caseDetail.theory.split('\n').map((line, index) => {
                      if (line.startsWith('## ')) {
                        return (
                          <Typography key={index} variant="h5" component="h2" sx={{ mt: 3, mb: 2, fontWeight: 600 }}>
                            {line.replace('## ', '')}
                          </Typography>
                        )
                      } else if (line.startsWith('### ')) {
                        return (
                          <Typography key={index} variant="h6" component="h3" sx={{ mt: 2, mb: 1.5, fontWeight: 600 }}>
                            {line.replace('### ', '')}
                          </Typography>
                        )
                      } else if (line.trim()) {
                        return (
                          <Typography key={index} variant="body1" sx={{ mb: 1, lineHeight: 1.6 }}>
                            {line}
                          </Typography>
                        )
                      }
                      return <br key={index} />
                    })}
                  </Typography>
                </Paper>
              </CardContent>
            </Card>
          </Grid>

          {/* 右侧：相关推荐 */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h2" sx={{ mb: 3, fontWeight: 600 }}>
                  相关推荐
                </Typography>
                {[
                  { id: 2, title: '几何变换', icon: '🔄', level: 'basic' },
                  { id: 4, title: '二次函数图像', icon: '📊', level: 'intermediate' },
                  { id: 3, title: '三角函数', icon: '〰️', level: 'intermediate' },
                ].map((item) => (
                  <Box
                    key={item.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      mb: 1,
                      borderRadius: 2,
                      cursor: 'pointer',
                      transition: 'background-color 0.2s ease',
                      '&:hover': { bgcolor: '#f5f5f5' },
                    }}
                    onClick={() => navigate(`/cases/${item.id}`)}
                  >
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: 2,
                        background: 'linear-gradient(45deg, #667eea, #764ba2)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '1.5rem',
                        mr: 2,
                      }}
                    >
                      {item.icon}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {item.title}
                      </Typography>
                      <Chip
                        label={getLevelText(item.level)}
                        size="small"
                        sx={{
                          bgcolor: getLevelColor(item.level),
                          color: 'white',
                          fontSize: '0.7rem',
                          height: 20,
                          mt: 0.5,
                        }}
                      />
                    </Box>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default CaseDetailPage
