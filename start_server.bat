@echo off
chcp 65001 >nul
title 数学家-WEB 本地服务器

echo.
echo ============================================================
echo                   🧮 数学家-WEB 本地服务器
echo ============================================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: 显示Python版本
echo 🐍 Python环境检查:
python --version

:: 检查关键文件
echo.
echo 📁 文件检查:
if exist "frontend\index.html" (
    echo    ✅ frontend\index.html - 主页面
) else (
    echo    ❌ frontend\index.html - 文件缺失
)

if exist "media\videos" (
    echo    ✅ media\videos - 动画目录
) else (
    echo    ⚠️  media\videos - 目录不存在，请先生成动画
)

if exist "README.md" (
    echo    ✅ README.md - 项目说明
) else (
    echo    ⚠️  README.md - 文件缺失
)

:: 检查动画文件
echo.
echo 🎬 动画文件检查:
set video_count=0

if exist "media\videos\1080p60\SimpleTest.mp4" (
    echo    ✅ SimpleTest.mp4 - 基础测试动画
    set /a video_count+=1
)

if exist "media\videos\simple_linear_function\480p15\SimpleLinearFunction.mp4" (
    echo    ✅ SimpleLinearFunction.mp4 - 一次函数动画
    set /a video_count+=1
)

if exist "media\videos\simple_geometry\480p15\SimpleGeometry.mp4" (
    echo    ✅ SimpleGeometry.mp4 - 几何变换动画
    set /a video_count+=1
)

if exist "media\videos\test_integration\480p15\IntegrationTest.mp4" (
    echo    ✅ IntegrationTest.mp4 - 系统集成测试
    set /a video_count+=1
)

if exist "media\videos\test_integration\480p15\QuickDemo.mp4" (
    echo    ✅ QuickDemo.mp4 - 快速演示动画
    set /a video_count+=1
)

echo    📊 发现 %video_count% 个动画文件

if %video_count% equ 0 (
    echo.
    echo ⚠️  警告: 未发现动画文件
    echo    请先运行以下命令生成动画:
    echo    python -m manim -ql manim-scripts\basic\simple_linear_function.py SimpleLinearFunction
    echo    python -m manim -ql manim-scripts\basic\simple_geometry.py SimpleGeometry
    echo    python -m manim -ql manim-scripts\basic\test_integration.py IntegrationTest
    echo    python -m manim -ql manim-scripts\basic\test_integration.py QuickDemo
    echo.
)

:: 启动服务器
echo.
echo 🚀 启动本地服务器...
echo.

:: 尝试不同端口
set port=8000
:try_port
netstat -an | find ":%port%" >nul
if not errorlevel 1 (
    echo ⚠️  端口 %port% 已被占用，尝试端口 %port%1
    set port=%port%1
    goto try_port
)

echo 🌐 服务器信息:
echo    📍 端口: %port%
echo    📁 目录: %cd%
echo    🔗 主页: http://localhost:%port%/frontend/index.html
echo    🎬 测试: http://localhost:%port%/frontend/test-fullscreen.html
echo    📚 文档: http://localhost:%port%/README.md
echo.
echo 💡 使用说明:
echo    • 服务器启动后会自动打开浏览器
echo    • 按 Ctrl+C 停止服务器
echo    • 关闭此窗口也会停止服务器
echo.

:: 延迟启动浏览器
start "" /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:%port%/frontend/index.html"

:: 启动Python HTTP服务器
echo ✅ 服务器启动成功！正在运行...
echo.
python start_server.py

:: 服务器停止后的清理
echo.
echo 🛑 服务器已停止
echo 感谢使用数学家-WEB！
echo.
pause
