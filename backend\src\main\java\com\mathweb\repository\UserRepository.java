package com.mathweb.repository;

import com.mathweb.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问接口
 * 
 * <AUTHOR> Animator Team
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    Optional<User> findByUsernameOrEmail(String username, String email);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 根据角色查找用户
     */
    List<User> findByRole(User.UserRole role);

    /**
     * 根据角色分页查找用户
     */
    Page<User> findByRole(User.UserRole role, Pageable pageable);

    /**
     * 查找活跃用户
     */
    List<User> findByIsActiveTrue();

    /**
     * 查找已验证邮箱的用户
     */
    List<User> findByEmailVerifiedTrue();

    /**
     * 根据角色和活跃状态查找用户
     */
    Page<User> findByRoleAndIsActive(User.UserRole role, Boolean isActive, Pageable pageable);

    /**
     * 查找指定时间之后创建的用户
     */
    List<User> findByCreatedAtAfter(LocalDateTime dateTime);

    /**
     * 查找指定时间之后登录的用户
     */
    List<User> findByLastLoginAtAfter(LocalDateTime dateTime);

    /**
     * 根据显示名称模糊查找用户
     */
    Page<User> findByDisplayNameContainingIgnoreCase(String displayName, Pageable pageable);

    /**
     * 根据用户名模糊查找用户
     */
    Page<User> findByUsernameContainingIgnoreCase(String username, Pageable pageable);

    /**
     * 更新用户最后登录时间
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginAt = :loginTime WHERE u.id = :userId")
    void updateLastLoginTime(@Param("userId") Long userId, @Param("loginTime") LocalDateTime loginTime);

    /**
     * 更新用户邮箱验证状态
     */
    @Modifying
    @Query("UPDATE User u SET u.emailVerified = :verified WHERE u.id = :userId")
    void updateEmailVerificationStatus(@Param("userId") Long userId, @Param("verified") Boolean verified);

    /**
     * 更新用户活跃状态
     */
    @Modifying
    @Query("UPDATE User u SET u.isActive = :active WHERE u.id = :userId")
    void updateActiveStatus(@Param("userId") Long userId, @Param("active") Boolean active);

    /**
     * 统计各角色用户数量
     */
    @Query("SELECT u.role, COUNT(u) FROM User u GROUP BY u.role")
    List<Object[]> countUsersByRole();

    /**
     * 统计活跃用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.isActive = true")
    long countActiveUsers();

    /**
     * 统计已验证邮箱的用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.emailVerified = true")
    long countVerifiedUsers();

    /**
     * 查找最近注册的用户
     */
    @Query("SELECT u FROM User u ORDER BY u.createdAt DESC")
    Page<User> findRecentUsers(Pageable pageable);

    /**
     * 查找最近活跃的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt IS NOT NULL ORDER BY u.lastLoginAt DESC")
    Page<User> findRecentActiveUsers(Pageable pageable);

    /**
     * 根据多个条件搜索用户
     */
    @Query("SELECT u FROM User u WHERE " +
           "(:username IS NULL OR LOWER(u.username) LIKE LOWER(CONCAT('%', :username, '%'))) AND " +
           "(:email IS NULL OR LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%'))) AND " +
           "(:role IS NULL OR u.role = :role) AND " +
           "(:isActive IS NULL OR u.isActive = :isActive)")
    Page<User> searchUsers(@Param("username") String username,
                          @Param("email") String email,
                          @Param("role") User.UserRole role,
                          @Param("isActive") Boolean isActive,
                          Pageable pageable);
}
