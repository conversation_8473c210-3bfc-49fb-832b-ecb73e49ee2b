package com.mathweb;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 数学动画WEB平台主应用类
 * 
 * <AUTHOR> Animator Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAsync
public class MathAnimatorApplication {

    public static void main(String[] args) {
        SpringApplication.run(MathAnimatorApplication.class, args);
    }
}
