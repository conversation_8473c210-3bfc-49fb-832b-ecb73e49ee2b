import React from 'react'
import {
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Box,
  AppBar,
  Toolbar,
  IconButton,
} from '@mui/material'
import {
  School,
  Animation,
  Category,
  Settings,
  PlayArrow,
  MenuBook,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const HomePage: React.FC = () => {
  const navigate = useNavigate()

  const features = [
    {
      icon: <School sx={{ fontSize: 48, color: '#667eea' }} />,
      title: '面向中学生',
      description: '专为中学生设计的友好界面和适合的内容难度，让数学学习更轻松有趣。',
    },
    {
      icon: <Animation sx={{ fontSize: 48, color: '#667eea' }} />,
      title: '高质量动画',
      description: '基于Manim引擎的专业数学动画，让抽象概念变得生动直观。',
    },
    {
      icon: <Category sx={{ fontSize: 48, color: '#667eea' }} />,
      title: '丰富案例库',
      description: '涵盖几何、代数、函数等多个数学领域，满足不同学习需求。',
    },
    {
      icon: <Settings sx={{ fontSize: 48, color: '#667eea' }} />,
      title: '模块化设计',
      description: '便于管理员添加新的数学动画案例，持续扩展内容库。',
    },
  ]

  const sampleCases = [
    {
      id: 1,
      title: '一次函数动画',
      description: '通过动画演示一次函数 y = ax + b 的图像变化，理解参数对函数的影响。',
      level: 'basic',
      category: '代数',
      icon: '📈',
    },
    {
      id: 2,
      title: '几何变换',
      description: '展示平移、旋转、缩放等基本几何变换，帮助理解图形变化规律。',
      level: 'basic',
      category: '几何',
      icon: '🔄',
    },
    {
      id: 3,
      title: '三角函数',
      description: '动态展示正弦、余弦函数的图像生成过程和周期性特征。',
      level: 'intermediate',
      category: '三角函数',
      icon: '〰️',
    },
  ]

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'basic':
        return '#4caf50'
      case 'intermediate':
        return '#ff9800'
      case 'advanced':
        return '#f44336'
      default:
        return '#9e9e9e'
    }
  }

  const getLevelText = (level: string) => {
    switch (level) {
      case 'basic':
        return '基础'
      case 'intermediate':
        return '中级'
      case 'advanced':
        return '高级'
      default:
        return '未知'
    }
  }

  return (
    <Box sx={{ minHeight: '100vh' }}>
      {/* 导航栏 */}
      <AppBar position="static" sx={{ background: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(10px)' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            🧮 数学家-WEB
          </Typography>
          <Button color="inherit" onClick={() => navigate('/cases')}>
            案例库
          </Button>
          <Button color="inherit" onClick={() => navigate('/login')}>
            登录
          </Button>
        </Toolbar>
      </AppBar>

      {/* 英雄区域 */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: 10,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="lg">
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              fontWeight: 700,
              mb: 3,
              textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
            }}
          >
            数学家-WEB
          </Typography>
          <Typography
            variant="h4"
            component="h2"
            sx={{
              fontSize: { xs: '1.2rem', md: '1.5rem' },
              mb: 5,
              opacity: 0.9,
            }}
          >
            中学生数学动画学习平台
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<PlayArrow />}
              onClick={() => navigate('/cases')}
              sx={{
                bgcolor: 'white',
                color: '#667eea',
                '&:hover': { bgcolor: '#f5f5f5' },
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
              }}
            >
              开始学习
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<MenuBook />}
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': { borderColor: '#f5f5f5', bgcolor: 'rgba(255,255,255,0.1)' },
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
              }}
            >
              查看文档
            </Button>
          </Box>
        </Container>
      </Box>

      {/* 特性介绍 */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography
          variant="h3"
          component="h2"
          textAlign="center"
          sx={{ mb: 6, fontWeight: 600, color: '#333' }}
        >
          平台特色
        </Typography>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: 3,
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 16px 40px rgba(0,0,0,0.15)',
                  },
                }}
              >
                <CardContent>
                  <Box sx={{ mb: 2 }}>{feature.icon}</Box>
                  <Typography variant="h6" component="h3" sx={{ mb: 2, fontWeight: 600 }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* 示例案例 */}
      <Box sx={{ bgcolor: '#f8f9fa', py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            textAlign="center"
            sx={{ mb: 6, fontWeight: 600, color: '#333' }}
          >
            精选案例
          </Typography>
          <Grid container spacing={3}>
            {sampleCases.map((case_) => (
              <Grid item xs={12} md={4} key={case_.id}>
                <Card
                  sx={{
                    height: '100%',
                    cursor: 'pointer',
                    transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 28px rgba(0,0,0,0.15)',
                    },
                  }}
                  onClick={() => navigate(`/cases/${case_.id}`)}
                >
                  <Box
                    sx={{
                      height: 200,
                      background: 'linear-gradient(45deg, #667eea, #764ba2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '4rem',
                    }}
                  >
                    {case_.icon}
                  </Box>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6" component="h3" sx={{ fontWeight: 600 }}>
                        {case_.title}
                      </Typography>
                      <Box
                        sx={{
                          px: 1.5,
                          py: 0.5,
                          borderRadius: 2,
                          bgcolor: getLevelColor(case_.level),
                          color: 'white',
                          fontSize: '0.75rem',
                          fontWeight: 500,
                        }}
                      >
                        {getLevelText(case_.level)}
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.5 }}>
                      {case_.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      分类：{case_.category}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/cases')}
              sx={{ px: 4, py: 1.5 }}
            >
              查看更多案例
            </Button>
          </Box>
        </Container>
      </Box>

      {/* 页脚 */}
      <Box sx={{ bgcolor: '#333', color: 'white', py: 4, textAlign: 'center' }}>
        <Container maxWidth="lg">
          <Typography variant="body1" sx={{ mb: 2 }}>
            数学家-WEB - 让数学学习更生动有趣
          </Typography>
          <Typography variant="body2" color="rgba(255,255,255,0.7)">
            技术栈：React + Spring Boot + PostgreSQL + Manim | 版本：v1.0.0-dev
          </Typography>
        </Container>
      </Box>
    </Box>
  )
}

export default HomePage
