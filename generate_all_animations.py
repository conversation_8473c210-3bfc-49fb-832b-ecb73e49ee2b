"""
批量生成所有案例动画的脚本
"""

import os
import subprocess
import sys
from pathlib import Path

# 动画案例配置
ANIMATIONS_CONFIG = {
    "第一部分：基础几何与代数对应": {
        "part1_basic_geometry/case1_coordinate_points.py": {
            "class": "CoordinatePointsDemo",
            "title": "坐标系中的点",
            "description": "点的坐标表示与距离公式",
            "difficulty": "⭐⭐"
        },
        "part1_basic_geometry/case2_line_equations.py": {
            "class": "LineEquationsDemo",
            "title": "直线的方程",
            "description": "点斜式、截距式、一般式的相互转换",
            "difficulty": "⭐⭐⭐"
        },
        "part1_basic_geometry/case3_circle_equations.py": {
            "class": "CircleEquationsDemo",
            "title": "圆的方程",
            "description": "圆的形成过程与方程推导",
            "difficulty": "⭐⭐⭐"
        }
    },
    "第二部分：函数图像与几何变换": {
        "part2_functions/case4_linear_function_transform.py": {
            "class": "LinearFunctionTransformDemo",
            "title": "一次函数图像变换",
            "description": "从 y=kx 到 y=kx+b 的图像变化",
            "difficulty": "⭐⭐"
        },
        "part2_functions/case5_quadratic_function.py": {
            "class": "QuadraticFunctionDemo",
            "title": "二次函数的图像性质",
            "description": "抛物线的开口、顶点、对称轴",
            "difficulty": "⭐⭐⭐⭐"
        },
        "part2_functions/case6_inverse_function.py": {
            "class": "InverseFunctionDemo",
            "title": "反比例函数的双曲线",
            "description": "y = k/x 的图像特征与渐近线",
            "difficulty": "⭐⭐⭐"
        }
    },
    "第三部分：三角形的几何与代数": {
        "part3_triangles/case7_triangle_area.py": {
            "class": "TriangleAreaDemo",
            "title": "三角形面积公式的推导",
            "description": "从矩形面积到三角形面积",
            "difficulty": "⭐⭐⭐"
        }
    }
}

def run_manim_command(script_path, class_name, quality="ql"):
    """运行Manim命令生成动画"""
    try:
        cmd = [
            "python", "-m", "manim",
            f"-{quality}",  # 质量设置
            f"manim-scripts/cases/{script_path}",
            class_name
        ]

        print(f"正在生成: {class_name}")
        print(f"命令: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")

        if result.returncode == 0:
            print(f"✅ 成功生成: {class_name}")
            return True
        else:
            print(f"❌ 生成失败: {class_name}")
            print(f"错误信息: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return False

def create_directories():
    """创建必要的目录结构"""
    directories = [
        "manim-scripts/cases/part1_basic_geometry",
        "manim-scripts/cases/part2_functions",
        "manim-scripts/cases/part3_triangles",
        "manim-scripts/cases/part4_circles",
        "manim-scripts/cases/part5_transformations",
        "media/videos/cases"
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 创建目录: {directory}")

def generate_animation_index():
    """生成动画索引文件"""
    index_content = """# 数学动画案例索引

## 📚 案例列表

"""

    for section, cases in ANIMATIONS_CONFIG.items():
        index_content += f"### {section}\n\n"

        for script_path, config in cases.items():
            index_content += f"#### {config['title']} {config['difficulty']}\n"
            index_content += f"- **描述**: {config['description']}\n"
            index_content += f"- **脚本**: `{script_path}`\n"
            index_content += f"- **类名**: `{config['class']}`\n"
            index_content += f"- **视频路径**: `media/videos/cases/{config['class'].lower()}/480p15/{config['class']}.mp4`\n\n"

    index_content += """
## 🎬 生成说明

使用以下命令生成单个动画：
```bash
python -m manim -ql manim-scripts/cases/[script_path] [ClassName]
```

使用批量生成脚本：
```bash
python generate_all_animations.py
```

## 📊 质量设置

- `-ql`: 低质量 (480p15)
- `-qm`: 中等质量 (720p30)
- `-qh`: 高质量 (1080p60)
- `-qk`: 4K质量 (2160p60)
"""

    with open("ANIMATION_INDEX.md", "w", encoding="utf-8") as f:
        f.write(index_content)

    print("📄 生成动画索引文件: ANIMATION_INDEX.md")

def main():
    """主函数"""
    print("🎬 开始批量生成数学动画...")
    print("=" * 50)

    # 创建目录结构
    create_directories()

    # 生成动画索引
    generate_animation_index()

    # 统计信息
    total_animations = sum(len(cases) for cases in ANIMATIONS_CONFIG.values())
    successful = 0
    failed = 0

    print(f"\n📊 总共需要生成 {total_animations} 个动画")
    print("=" * 50)

    # 逐个生成动画
    for section, cases in ANIMATIONS_CONFIG.items():
        print(f"\n📚 {section}")
        print("-" * 30)

        for script_path, config in cases.items():
            success = run_manim_command(script_path, config["class"])

            if success:
                successful += 1
            else:
                failed += 1

            print()  # 空行分隔

    # 生成总结报告
    print("=" * 50)
    print("📊 生成完成统计:")
    print(f"✅ 成功: {successful}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {successful/total_animations*100:.1f}%")

    if failed > 0:
        print("\n⚠️  部分动画生成失败，请检查:")
        print("1. Manim是否正确安装")
        print("2. Python路径是否正确")
        print("3. 脚本文件是否存在语法错误")

    print("\n🎉 批量生成任务完成！")

if __name__ == "__main__":
    main()
